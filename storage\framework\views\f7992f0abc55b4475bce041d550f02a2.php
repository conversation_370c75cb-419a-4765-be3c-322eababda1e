<style>
    /* Modern Sidebar Styling */
    #sidenav-main {
        background-color: #ffffff !important;
        box-shadow: 0 0 30px rgba(0, 0, 0, 0.05);
    }

    .navbar-vertical {
    background-color: #ffffff;
    border-right: 1px solid #e5e7eb;

}

.navbar-vertical .navbar-nav {
    padding: 20px 0;
    list-style: none;
    margin: 0;
}

.navbar-vertical .navbar-nav .nav-item {
    margin: 3px 0;
}

.navbar-vertical .navbar-nav .nav-link {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: #374151;
    font-weight: 500;
    font-size: 14px;
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.2s ease-in-out;
    position: relative;
    margin: 0 12px;

}

.navbar-vertical .navbar-nav .nav-link i {
    margin-right: 12px;
    font-size: 16px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background-color: transparent;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* 3D Icon Styles */
.icon-3d {
    width: 48px;
    height: 48px;
    margin-right: 12px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
}

.icon-3d svg {
    width: 26px;
    height: 26px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.icon-3d:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
}

/* Individual 3D Icon Backgrounds - Green Gradient */
.icon-templates-3d {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.icon-miniapps-3d {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.icon-orders-3d {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.icon-chatbot-3d {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.icon-qrcode-3d {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

/* Hover Effect */
.navbar-vertical .navbar-nav .nav-link:hover {
    background-color: #21c45d;
    color: #ffffffff;
}

.navbar-vertical .navbar-nav .nav-link:hover i {
    background-color: #ffffffff;
    color: #22c55e;
}

.navbar-vertical .navbar-nav .nav-link:hover .icon-3d {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Active State - Green Theme with White Icon Background */
.navbar-vertical .navbar-nav .nav-link.active {
    background-color: #22c55e;
    color: #ffffff;
    font-weight: 600;
    border-radius: 12px;
}

.navbar-vertical .navbar-nav .nav-link.active i {
    background-color: #ffffff;
    color: #22c55e;
    border-radius: 8px;
    font-weight: 600;
}

.navbar-vertical .navbar-nav .nav-link.active .icon-3d {
    width: 52px;
    height: 52px;
    background: #ffffff !important;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: scale(1.05);
}

.navbar-vertical .navbar-nav .nav-link.active .icon-3d svg {
    width: 28px;
    height: 28px;
}

.navbar-vertical .navbar-nav .nav-link.active .icon-3d svg path,
.navbar-vertical .navbar-nav .nav-link.active .icon-3d svg rect,
.navbar-vertical .navbar-nav .nav-link.active .icon-3d svg circle {
    fill: #22c55e !important;
}

.navbar-vertical .navbar-nav .nav-link.active:hover {
    background-color: #16a34a;
}

.navbar-vertical .navbar-nav .nav-link.active:hover i {
    background-color: #ffffff;
    color: #16a34a;
}

.navbar-vertical .navbar-nav .nav-link.active:hover .icon-3d svg path,
.navbar-vertical .navbar-nav .nav-link.active:hover .icon-3d svg rect,
.navbar-vertical .navbar-nav .nav-link.active:hover .icon-3d svg circle {
    fill: #16a34a !important;
}
    

    
    /* Submenu styling */
    .lw-expandable-nav {
        padding-left: 10px;
        margin-top: 5px;
    }
    
    .nav-link-ul {
        font-size: 12px !important;
        padding: 6px 10px 6px 30px !important;
        position: relative;
    }
    .navbar-vertical.navbar-expand-md .navbar-nav .nav-link {
        padding: 10px 10px 10px 10px !important;
    }
    .nav-link-ul::before {
        content: '';
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: #c0c6cc;
    }
    
    
    .nav-link-ul:hover::before {
        background-color: #1771E6;
    }
    
    .nav-link-ul.active {
        color: #1771E6 !important;
        font-weight: 600;
    }
   
    .nav-link-ul.active::before {
        background-color: #1771E6;
    }
    
    /* Dropdown indicators */
    .nav-link[data-toggle="collapse"]::after {
        content: '';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        position: absolute;
        right: 20px;
        transition: transform 0.2s ease;
        
    }
    
    .nav-link[data-toggle="collapse"][aria-expanded="true"]::after {
        transform: rotate(90deg);
    }
    
    /* Section dividers */
    .sidebar-section-divider {
        height: 1px;
        background-color: #edf0f5;
        margin: 15px 20px;
    }
    
    /* Active submenu background */
   
</style>

<!-- Update the icon classes in the navbar -->
<nav class="navbar navbar-vertical fixed-left navbar-expand-md text-dark lw-sidebar-container" id="sidenav-main">
    <div class="container-fluid">
        <span>
            <!-- Toggler -->
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#sidenav-collapse-main"
        aria-controls="sidenav-main" aria-expanded="false" aria-label="Toggle navigation">
        <i class="fas fa-bars"></i>
    </button>
    <!-- Brand -->
    <!-- <a class="navbar-brand pt-0 d-none d-sm-inline" href="<?php echo e(url('https://crm.myaiplanet.com')); ?>">
        <img src="<?php echo e(getAppSettings('logo_image_url')); ?>" class="navbar-brand-img lw-sidebar-logo-normal" alt="<?php echo e(getAppSettings('name')); ?>">
        <img src="<?php echo e(getAppSettings('small_logo_image_url')); ?>" class="navbar-brand-img lw-sidebar-logo-small" alt="<?php echo e(getAppSettings('name')); ?>">
    </a> -->
        </span>
        <!-- User -->
        <ul class="nav align-items-center d-md-none">
            <li class="nav-item">
                <?php echo $__env->make('layouts.navbars.locale-menu', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
              </li>
            <li class="nav-item dropdown">
                <a class="nav-link" href="#" role="button" data-toggle="dropdown" aria-haspopup="true"
                    aria-expanded="false">
                    <div class="media align-items-center">
                        <span class="avatar avatar-sm rounded-circle">
                            <i class="fa fa-user"></i>
                        </span>
                    </div>
                </a>
                <div class="dropdown-menu dropdown-menu-arrow dropdown-menu-right">
                    <div class=" dropdown-header noti-title">
                        <h6 class="text-overflow m-0"><?php echo e(__tr('Welcome!')); ?></h6>
                    </div>
                    <a href="<?php echo e(route('user.profile.edit')); ?>" class="dropdown-item">
                        <i class="fa fa-user"></i>
                        <span><?php echo e(__tr('My profile')); ?></span>
                    </a>
                    <div class="dropdown-divider"></div>
                    <a data-method="post" href="<?php echo e(route('auth.logout')); ?>" class="dropdown-item lw-ajax-link-action">
                        <i class="fas fa-sign-out-alt"></i>
                        <span><?php echo e(__tr('Logout')); ?></span>
                    </a>
                </div>
            </li>
        </ul>
        <!-- Collapse -->
        <div class="collapse navbar-collapse" id="sidenav-collapse-main">
            <!-- Collapse header -->
            <div class="navbar-collapse-header d-md-none">
                <div class="row">
                    <div class="col-6 collapse-brand">
                        <a href="<?php echo e(url('/')); ?>">
                            <img src="<?php echo e(getAppSettings('logo_image_url')); ?>">
                        </a>
                    </div>
                    <div class="col-6 collapse-close">
                        <button type="button" class="navbar-toggler" data-toggle="collapse"
                            data-target="#sidenav-collapse-main" aria-controls="sidenav-main" aria-expanded="false"
                            aria-label="Toggle sidenav">
                            <span></span>
                            <span></span>
                        </button>
                    </div>
                </div>
            </div>
            <!-- Navigation -->
            <ul class="navbar-nav">
                <?php if(hasCentralAccess()): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(markAsActiveLink('central.console')); ?>" href="<?php echo e(route('central.console')); ?>">
                        <i class="fa fa-chart-line icon-dashboard"></i> <?php echo e(__tr('Dashboard')); ?>

                    </a>
                </li>
               
                <li class="nav-item">
                    <a class="nav-link" href="#lwSubscriptionSubMenu" data-toggle="collapse" role="button"
                        aria-expanded="false" aria-controls="lwSubscriptionSubMenu">
                        <i class="fa fa-wallet icon-wallet"></i>
                        <span class="nav-link-text"><?php echo e(__tr('User Plans')); ?></span>
                    </a>
                    <div class="collapse show lw-expandable-nav" id="lwSubscriptionSubMenu">
                        <ul class="nav nav-sm flex-column">
                            <li class="nav-item <?php echo e(markAsActiveLink('central.subscriptions')); ?>">
                                <a class="bg-primary-light nav-link nav-link-ul" href="<?php echo e(route('central.subscriptions')); ?>">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo e(__tr('Auto')); ?>

                                </a>
                            </li>
                            <li class="nav-item <?php echo e(markAsActiveLink('central.subscription.manual_subscription.read.list_view')); ?>">
                                <a class="nav-link nav-link-ul bg-primary-light" href="<?php echo e(route('central.subscription.manual_subscription.read.list_view')); ?>">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo e(__tr('Manual/Prepaid')); ?> <?php if(getPendingSubscriptionCount()): ?><span class="badge badge-danger ml-2"><?php echo e(getPendingSubscriptionCount()); ?></span> <?php endif; ?>
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                
                <li class="nav-item  ">
                    <a class="nav-link"  href="<?php echo e(route('central.vendors')); ?>" >
                        <i class="fas fa-users icon-users"></i> <?php echo e(__tr('Users')); ?>

                    </a>
                    
                </li>
                
                <li class="nav-item ">
                    <a class="nav-link <?php echo e(markAsActiveLink('page.list')); ?>" href="<?php echo e(route('page.list')); ?>">
                        <i class="fas fa-copy icon-pages"></i> <?php echo e(__tr('Pages')); ?>

                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo e(markAsActiveLink('manage.translations.languages')); ?>" href="<?php echo e(route('manage.translations.languages')); ?>">
                        <i class="fas fa-globe icon-globe"></i> <?php echo e(__tr('Languages')); ?>

                    </a>
                </li>


                
                <li class="nav-item <?php echo e(request('pageType') == 'other' ? 'active' : ''); ?>">
                    <a class="bg-primary-light nav-link nav-link-footer"
                        href="<?php echo e(route('manage.configuration.read', ['pageType' => 'other'])); ?>">
                        <i class="fa fa-cogs icon-settings"></i>
                        <?php echo __tr('Setup'); ?>

                    </a>
                </li>
                
                <li class="nav-item <?php echo e(request('pageType') == 'whatsapp-onboarding' ? 'active' : ''); ?>">
                    <a class="bg-primary-light nav-link nav-link-footer"
                        href="<?php echo e(route('manage.configuration.read', ['pageType' => 'whatsapp-onboarding'])); ?>">
                        <i class="fab fa-facebook icon-facebook"></i>
                        <?php echo __tr('Embedded Signup'); ?>

                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link" href="#configurationMenu" data-toggle="collapse" role="button"
                        aria-expanded="false" aria-controls="configurationMenu">
                        <i class="fa fa-tools icon-tools"></i>
                        <span class="nav-link-text"><?php echo e(__tr('Settings')); ?></span>
                    </a>

                    <div class="collapse show lw-expandable-nav" id="configurationMenu">
                        <ul class="nav nav-sm flex-column">
                            <li class="nav-item">
                                <a class="bg-primary-light nav-link nav-link-ul <?php echo e(request('pageType') == 'general' ? 'active' : ''); ?>"
                                    href="<?php echo e(route('manage.configuration.read', ['pageType' => 'general'])); ?>">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo e(__tr('General')); ?>

                                </a>
                            </li>
                            <li class="nav-item <?php echo e(request('pageType') == 'user' ? 'active' : ''); ?>">
                                <a class="bg-primary-light nav-link nav-link-ul"
                                    href="<?php echo e(route('manage.configuration.read', ['pageType' => 'user'])); ?>">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo __tr('User'); ?>

                                </a>
                            </li>
                            <li class="nav-item <?php echo e(request('pageType') == 'currency' ? 'active' : ''); ?>">
                                <a class="bg-primary-light nav-link nav-link-ul"
                                    href="<?php echo e(route('manage.configuration.read', ['pageType' => 'currency'])); ?>">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo e(__tr('Currency')); ?>

                                </a>
                            </li>
                            <li class="nav-item <?php echo e(markAsActiveLink('manage.configuration.payment')); ?>">
                                <a class="bg-primary-light nav-link-ul nav-link <?= (isset($pageType) and $pageType == 'payment') ? 'active' : '' ?>"
                                    href="<?= route('manage.configuration.read', ['pageType' => 'payment']) ?>">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo e(__tr('Payments')); ?>

                                </a>
                            </li>
                            <li class="nav-item <?php echo e(markAsActiveLink('manage.configuration.subscription-plans')); ?>">
                                <a class="bg-primary-light nav-link nav-link-ul" href="<?php echo e(route('manage.configuration.subscription-plans')); ?>">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo e(__tr('User Plans')); ?>

                                </a>
                            </li>
                            <li class="nav-item <?php echo e(request('pageType') == 'email' ? 'active' : ''); ?>">
                                <a class="bg-primary-light nav-link nav-link-ul"
                                    href="<?php echo e(route('manage.configuration.read', ['pageType' => 'email'])); ?>">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo e(__tr('Email')); ?>

                                </a>
                            </li>
                            <li class="nav-item <?php echo e(request('pageType') == 'social-login' ? 'active' : ''); ?>">
                                <a class="bg-primary-light nav-link nav-link-ul"
                                    href="<?php echo e(route('manage.configuration.read', ['pageType' => 'social-login'])); ?>">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo e(__tr('Logins')); ?>

                                </a>
                            </li>
                            <li class="nav-item <?php echo e(request('pageType') == 'misc' ? 'active' : ''); ?>">
                                <a class="bg-primary-light nav-link nav-link-ul"
                                    href="<?php echo e(route('manage.configuration.read', ['pageType' => 'misc'])); ?>">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo __tr('Apperance'); ?>

                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                
                <!--li class="nav-item <!--?= Request::fullUrl() == route('manage.configuration.read', ['pageType' => 'licence-information']) ? 'active' : '' ?>"-->
                    <!--a class="bg-primary-light nav-link nav-link-footer"  href="<!--?= route('manage.configuration.read', ['pageType' => 'licence-information']) ?>"-->
                        <!--i class="fas fa-shield-alt" style="color: #20C997 !important"></i-->
                        <!--span--><!--?= __tr('License') ?></span-->
                    <!--/a>
                </li---->
                
                <?php endif; ?>
                <?php if(hasVendorAccess() or hasVendorUserAccess()): ?>
                <li class="nav-item">
                    <a class="nav-link" href="https://crm.myaiplanet.com">
                        <i class="fa fa-arrow-left icon-dashboard"></i>
                        <?php echo e(__tr('Back')); ?>

                    </a>
                </li> 
                 <?php if(hasVendorAccess('messaging') && hasModuleAccess('whatsapp_flows')): ?>
                <!-- <li class="nav-item">
                    <a class="nav-link <?php echo e(markAsActiveLink('vendor.chat_message.contact.view')); ?>" href="<?php echo e(route('vendor.chat_message.contact.view')); ?>"
                    <?php if(!isWhatsAppBusinessAccountReady()): ?>
                       onclick="alertAndRedirect(event, '<?php echo e(route('vendor.settings.read', ['pageType' => 'whatsapp-cloud-api-setup'])); ?>')"
                   <?php endif; ?>>
                        <span x-cloak x-show="unreadMessagesCount" class="badge badge-success rounded-pill ml--2" x-text="unreadMessagesCount"></span>
                        <i class="fab fa-whatsapp icon-chat"></i> <span class="ml--2"><?php echo e(__tr('WhatsApp Chat')); ?></span>
                    </a>
                </li> -->
                <?php endif; ?>

                <!-- Instagram Chat -->
                <!-- <?php if(hasVendorAccess('messaging') && getVendorSettings('enable_instagram_messaging')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(markAsActiveLink('vendor.instagram_chat.contact.view')); ?>" href="<?php echo e(route('vendor.instagram_chat.contact.view')); ?>">
                        <i class="fab fa-instagram icon-chat text-danger"></i> <span class="ml--2"><?php echo e(__tr('Instagram Chat')); ?></span>
                    </a>
                </li>
                <?php endif; ?> -->

                <!-- Facebook Chat -->
                <!-- <?php if(hasVendorAccess('messaging') && getVendorSettings('enable_facebook_messaging')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(markAsActiveLink('vendor.facebook_chat.contact.view')); ?>" href="<?php echo e(route('vendor.facebook_chat.contact.view')); ?>">
                        <i class="fab fa-facebook icon-chat text-primary"></i> <span class="ml--2"><?php echo e(__tr('Facebook Chat')); ?></span>
                    </a>
                </li>
                <?php endif; ?> -->

                <!-- Add Unified Live Chat as separate menu item -->
                <!--  -->
                <?php if(hasVendorAccess('manage_templates') && hasModuleAccess('templates')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(markAsActiveLink('vendor.whatsapp_service.templates.read.list_view')); ?>"
                        href="<?php echo e(route('vendor.whatsapp_service.templates.read.list_view')); ?>"
                        <?php if(!isWhatsAppBusinessAccountReady()): ?>
                       onclick="alertAndRedirect(event, '<?php echo e(route('vendor.settings.read', ['pageType' => 'whatsapp-cloud-api-setup'])); ?>')"
                    <?php endif; ?>>
                        <div class="icon-3d icon-templates-3d">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="templateGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <path d="M4 4h16v2H4V4zm0 4h16v2H4V8zm0 4h16v2H4v-2zm0 4h10v2H4v-2z" fill="url(#templateGrad)"/>
                                <path d="M16 16h4v4h-4v-4z" fill="url(#templateGrad)" opacity="0.8"/>
                            </svg>
                        </div>
                        <?php echo e(__tr('Templates')); ?>

                    </a>
                </li>
                <?php endif; ?>
                <!-- <?php if(hasVendorAccess('manage_campaigns') && hasModuleAccess('campaigns')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(markAsActiveLink('vendor.campaign.read.list_view')); ?>"
                        href="<?php echo e(route('vendor.campaign.read.list_view')); ?>"
                        <?php if(!isWhatsAppBusinessAccountReady()): ?>
                       onclick="alertAndRedirect(event, '<?php echo e(route('vendor.settings.read', ['pageType' => 'whatsapp-cloud-api-setup'])); ?>')"
                   <?php endif; ?>>
                        <i class="fa fa-rocket icon-campaigns "></i>
                        <?php echo e(__tr('Campaigns')); ?>

                    </a>
                </li>
                <?php endif; ?> -->
                <?php if(hasVendorAccess('manage_flows') && hasModuleAccess('whatsapp_flows')): ?>
               

                <li class="nav-item">
                    <a class="nav-link <?php echo e(markAsActiveLink('whatsapp-flows.index')); ?>"
                        href="<?php echo e(route('whatsapp-flows.index')); ?>"
                        <?php if(!isWhatsAppBusinessAccountReady()): ?>
                       onclick="alertAndRedirect(event, '<?php echo e(route('vendor.settings.read', ['pageType' => 'whatsapp-cloud-api-setup'])); ?>')"
                    <?php endif; ?>>
                        <div class="icon-3d icon-miniapps-3d">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="miniappsGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <rect x="3" y="3" width="7" height="7" rx="2" fill="url(#miniappsGrad)"/>
                                <rect x="14" y="3" width="7" height="7" rx="2" fill="url(#miniappsGrad)" opacity="0.9"/>
                                <rect x="3" y="14" width="7" height="7" rx="2" fill="url(#miniappsGrad)" opacity="0.8"/>
                                <rect x="14" y="14" width="7" height="7" rx="2" fill="url(#miniappsGrad)" opacity="0.7"/>
                            </svg>
                        </div>
                        <?php echo e(__tr('Mini Apps')); ?>

                    </a>
                </li>
                <?php endif; ?>
                <?php if(hasVendorAccess('administrative') && hasModuleAccess('whatsapp_orders')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(markAsActiveLink('vendor.whatsapp.orders.list')); ?>"
                        href="<?php echo e(route('vendor.whatsapp.orders.list')); ?>"
                        <?php if(!isWhatsAppBusinessAccountReady()): ?>
                       onclick="alertAndRedirect(event, '<?php echo e(route('vendor.settings.read', ['pageType' => 'whatsapp-cloud-api-setup'])); ?>')"
                   <?php endif; ?>>
                        <div class="icon-3d icon-orders-3d">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="ordersGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V19H17V6H7Z" fill="url(#ordersGrad)"/>
                                <circle cx="10" cy="12" r="1.5" fill="url(#ordersGrad)" opacity="0.8"/>
                                <circle cx="14" cy="12" r="1.5" fill="url(#ordersGrad)" opacity="0.8"/>
                            </svg>
                        </div>
                        <?php echo e(__tr('WhatsApp Orders')); ?>

                    </a>
                </li>
                <?php endif; ?>
                <!-- <?php if(hasVendorAccess('manage_contacts')  ): ?>
                <li class="nav-item">
                    <a class="nav-link" href="#vendorContactSubmenuNav" data-toggle="collapse" role="button"
                        aria-expanded="false" aria-controls="vendorContactSubmenuNav"
                        <?php if(!isWhatsAppBusinessAccountReady()): ?>
                       onclick="alertAndRedirect(event, '<?php echo e(route('vendor.settings.read', ['pageType' => 'whatsapp-cloud-api-setup'])); ?>')"
                   <?php endif; ?>>
                        <i class="fa fa-users icon-users "></i>
                        <span class=""><?php echo e(__tr('Contacts')); ?></span>
                    </a>
                <div class="collapse lw-expandable-nav" id="vendorContactSubmenuNav">
                    <ul class="nav nav-sm flex-column">
                        <li class="nav-item">
                            <a class="nav-link nav-link-ul <?php echo e(markAsActiveLink('vendor.contact.read.list_view')); ?>"
                                href="<?php echo e(route('vendor.contact.read.list_view')); ?>">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo e(__tr('All Contacts')); ?>

                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link nav-link-ul <?php echo e(markAsActiveLink('vendor.contact.group.read.list_view')); ?>"
                                href="<?php echo e(route('vendor.contact.group.read.list_view')); ?>">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo e(__tr('Contact Groups')); ?>

                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link nav-link-ul <?php echo e(markAsActiveLink('vendor.contact.custom_field.read.list_view')); ?>"
                                href="<?php echo e(route('vendor.contact.custom_field.read.list_view')); ?>">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo e(__tr('Add Input')); ?>

                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?> -->
                 <?php if(hasVendorAccess('manage_bot_replies') && hasModuleAccess('chatbot')): ?>
                 <li class="nav-item">
                    <a class="nav-link" href="#vendorAutomationSubmenuNav" data-toggle="collapse" role="button"
                        aria-expanded="false" aria-controls="vendorAutomationSubmenuNav"
                        <?php if(!isWhatsAppBusinessAccountReady()): ?>
                       onclick="alertAndRedirect(event, '<?php echo e(route('vendor.settings.read', ['pageType' => 'whatsapp-cloud-api-setup'])); ?>')"
                   <?php endif; ?>>
                        <div class="icon-3d icon-chatbot-3d">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="chatbotGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V9ZM19 9H14V4H5V21H19V9Z" fill="url(#chatbotGrad)"/>
                                <circle cx="9" cy="12" r="1" fill="url(#chatbotGrad)" opacity="0.8"/>
                                <circle cx="15" cy="12" r="1" fill="url(#chatbotGrad)" opacity="0.8"/>
                                <path d="M9 16H15V17H9V16Z" fill="url(#chatbotGrad)" opacity="0.7"/>
                            </svg>
                        </div>
                        <span class=""><?php echo e(__tr('Chatbot')); ?></span>
                    </a>
                <div class="collapse lw-expandable-nav" id="vendorAutomationSubmenuNav">
                    <ul class="nav nav-sm flex-column">
                        <li class="nav-item">
                        
                            <a class="nav-link nav-link-ul <?php echo e(markAsActiveLink('vendor.bot_reply.read.list_view')); ?>"
                                href="<?php echo e(route('vendor.bot_reply.read.list_view')); ?>">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo e(__tr('All Chatbots')); ?>

                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link nav-link-ul <?php echo e(markAsActiveLink('vendor.bot_reply.bot_flow.read.list_view')); ?>"
                                href="<?php echo e(route('vendor.bot_reply.bot_flow.read.list_view')); ?>">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo e(__tr('Flow Maker')); ?>

                            </a>
                        </li>
                    </ul>
                </div>
            </li>
                <?php endif; ?>
                <!-- <?php if(hasVendorAccess('administrative')  ): ?>

                <li class="nav-item">
                    <a class="nav-link <?php echo e(markAsActiveLink('vendor.user.read.list_view')); ?>"
                        href="<?php echo e(route('vendor.user.read.list_view')); ?>"
                        <?php if(!isWhatsAppBusinessAccountReady()): ?>
                       onclick="alertAndRedirect(event, '<?php echo e(route('vendor.settings.read', ['pageType' => 'whatsapp-cloud-api-setup'])); ?>')"
                   <?php endif; ?>>
                        <i class="fa fa-user-tie icon-agents"></i>
                        <?php echo e(__tr('Agents')); ?>

                    </a>
                </li>
                <?php endif; ?> -->
                <?php if(isWhatsAppBusinessAccountReady()): ?>
                <li class="nav-item">
                    <a class="nav-link" href="#" data-toggle="modal" data-target="#lwScanMeDialog">
                        <div class="icon-3d icon-qrcode-3d">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="qrcodeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <rect x="3" y="3" width="7" height="7" rx="1" fill="url(#qrcodeGrad)"/>
                                <rect x="14" y="3" width="7" height="7" rx="1" fill="url(#qrcodeGrad)"/>
                                <rect x="3" y="14" width="7" height="7" rx="1" fill="url(#qrcodeGrad)"/>
                                <rect x="5" y="5" width="3" height="3" fill="url(#qrcodeGrad)" opacity="0.7"/>
                                <rect x="16" y="5" width="3" height="3" fill="url(#qrcodeGrad)" opacity="0.7"/>
                                <rect x="5" y="16" width="3" height="3" fill="url(#qrcodeGrad)" opacity="0.7"/>
                                <rect x="14" y="12" width="2" height="2" fill="url(#qrcodeGrad)" opacity="0.8"/>
                                <rect x="17" y="12" width="2" height="2" fill="url(#qrcodeGrad)" opacity="0.8"/>
                                <rect x="12" y="14" width="2" height="2" fill="url(#qrcodeGrad)" opacity="0.8"/>
                                <rect x="12" y="17" width="2" height="2" fill="url(#qrcodeGrad)" opacity="0.8"/>
                            </svg>
                        </div>
                        <?php echo e(__tr('QR Code')); ?>

                    </a>
                </li>
                <?php endif; ?>
                <?php if(hasVendorAccess('administrative')): ?>
                <!-- <li class="nav-item">
                    <a class="nav-link <?php echo e(markAsActiveLink('subscription.read.show')); ?>"
                        href="<?php echo e(route('subscription.read.show')); ?>">
                        <i class="fa fa-wallet icon-plan"></i>
                        <?php echo e(__tr('My Plan')); ?>

                    </a>
                </li> -->
                <!-- <li class="nav-item">
                        <a class="nav-link <?php if(isWhatsAppBusinessAccountReady()): ?> collapsed <?php else: ?> text-warning <?php endif; ?>" href="#vendorSettingsNav" data-toggle="collapse" role="button"
                            aria-expanded="<?php echo !isWhatsAppBusinessAccountReady() ? 'true' : 'false'; ?>" aria-controls="vendorSettingsNav">
                            <i class="fa fa-cog icon-settings"></i>
                            <span class=""><?php echo e(__tr('Setup')); ?></span>
                        </a>
                    <div class="collapse <?php if(!isWhatsAppBusinessAccountReady()): ?> show <?php endif; ?> lw-expandable-nav" id="vendorSettingsNav">
                        <ul class="nav nav-sm flex-column">
                            <li class="nav-item">
                                <a class="nav-link nav-link-ul <?= (isset($pageType) and $pageType == 'general') ? 'active' : '' ?>"
                                    href="<?= route('vendor.settings.read', ['pageType' => 'general']) ?>"
                                    <?php if(!isWhatsAppBusinessAccountReady()): ?>
                       onclick="alertAndRedirect(event, '<?php echo e(route('vendor.settings.read', ['pageType' => 'whatsapp-cloud-api-setup'])); ?>')"
                   <?php endif; ?>>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo e(__tr('Basic')); ?>

                                </a>
                            </li>
                            <li class="nav-item">
                                <strong><a class="nav-link nav-link-ul <?= (isset($pageType) and $pageType == 'whatsapp-cloud-api-setup') ? 'active' : '' ?> <?php if(!isWhatsAppBusinessAccountReady()): ?> text-warning <?php endif; ?>"
                                    href="<?= route('vendor.settings.read', ['pageType' => 'whatsapp-cloud-api-setup']) ?>">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo e(__tr('WhatsApp Setup')); ?> <?php if(!isWhatsAppBusinessAccountReady()): ?><i class="fas fa-exclamation-triangle ml-1"></i><?php endif; ?>
                                </a></strong>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link nav-link-ul <?= (isset($pageType) and $pageType == 'instagram-api-setup') ? 'active' : '' ?> <?php if(!getVendorSettings('enable_instagram_messaging')): ?> text-info <?php endif; ?>"
                                    href="<?= route('vendor.settings.read', ['pageType' => 'instagram-api-setup']) ?>">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<i class="fab fa-instagram text-danger"></i> <?php echo e(__tr('Instagram Setup')); ?> <?php if(!getVendorSettings('enable_instagram_messaging')): ?><i class="fas fa-plus-circle ml-1"></i><?php endif; ?>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link nav-link-ul <?= (isset($pageType) and $pageType == 'facebook-api-setup') ? 'active' : '' ?> <?php if(!getVendorSettings('enable_facebook_messaging')): ?> text-info <?php endif; ?>"
                                    href="<?= route('vendor.settings.read', ['pageType' => 'facebook-api-setup']) ?>">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<i class="fab fa-facebook text-primary"></i> <?php echo e(__tr('Facebook Setup')); ?> <?php if(!getVendorSettings('enable_facebook_messaging')): ?><i class="fas fa-plus-circle ml-1"></i><?php endif; ?>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link nav-link-ul <?= (isset($pageType) and $pageType == 'ai-chat-bot-setup') ? 'active' : '' ?>"
                                    href="<?= route('vendor.settings.read', ['pageType' => 'ai-chat-bot-setup']) ?>"
                                    <?php if(!isWhatsAppBusinessAccountReady()): ?>
                       onclick="alertAndRedirect(event, '<?php echo e(route('vendor.settings.read', ['pageType' => 'whatsapp-cloud-api-setup'])); ?>')"
                   <?php endif; ?>>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo __tr('Chatbot Settings'); ?>

                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link nav-link-ul <?= (isset($pageType) and $pageType == 'whatsapp-orders-setup') ? 'active' : '' ?>"
                                    href="<?= route('vendor.settings.read', ['pageType' => 'whatsapp-orders-setup']) ?>"
                                    <?php if(!isWhatsAppBusinessAccountReady()): ?>
                       onclick="alertAndRedirect(event, '<?php echo e(route('vendor.settings.read', ['pageType' => 'whatsapp-cloud-api-setup'])); ?>')"
                   <?php endif; ?>>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo __tr('Orders & Payments'); ?>

                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link nav-link-ul <?= (isset($pageType) and $pageType == 'api-access') ? 'active' : '' ?>"
                                    href="<?= route('vendor.settings.read', ['pageType' => 'api-access']) ?>"
                                    >
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo __tr('API Integration'); ?>

                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link nav-link-ul <?= (isset($pageType) and $pageType == 'api-access') ? 'active' : '' ?>"
                                    href="<?= route('google-sheet-script.index', ['pageType' => 'api-access']) ?>">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo __tr('Sheets Integration'); ?>

                                </a>
                            </li>
                        </ul>
                    </div>
                </li> -->
                <?php endif; ?>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</nav><?php /**PATH C:\xampp\htdocs\omx-flow-new\resources\views/layouts/navbars/sidebar.blade.php ENDPATH**/ ?>