@extends('layouts.app', ['title' => __tr('Campaign Status')])
@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
@endpush
@section('content')
@include('users.partials.header', [
'title' => __tr(''),
'description' => '',
'class' => 'col-lg-7'
])
@php
$campaignData = $campaign->__data;
$selectedGroups = Arr::get($campaignData, 'selected_groups', []);
$isRestrictByTemplateContactLanguage = Arr::get($campaignData, 'is_for_template_language_only');
$isAllContacts = Arr::get($campaignData, 'is_all_contacts');
$messageLog = $campaign->messageLog;
$queueMessages = $campaign->queueMessages;
$campaignUid=$campaign->_uid;
$totalContacts = (int) Arr::get($campaignData, 'total_contacts');
$totalRead = $messageLog->where('status', 'read')->count();
$totalDelivered = $messageLog->where('status', 'delivered')->count();
$totalFailed = $queueMessages->where('status', 2)->count() + $messageLog->where('status', 'failed')->count();
$totalAccepted = $messageLog->where('status', 'accepted')->count();
$totalProcessing = $queueMessages->where('status', 3)->count();
$totalWaiting = $queueMessages->where('status', 1)->count();
$totalSent = $messageLog->where('status', 'sent')->count();
$totalProcessed = $totalSent + $totalRead + $totalDelivered + $totalAccepted + $totalFailed + $totalProcessing;

// Calculate percentages only if there are contacts
$totalReadInPercent = $totalContacts > 0 ? round(($totalRead / $totalContacts) * 100, 2) . '%' : '0%';
$totalDeliveredInPercent = $totalContacts > 0 ? round((($totalDelivered + $totalRead) / $totalContacts) * 100, 2) . '%' : '0%';
$totalFailedInPercent = $totalContacts > 0 ? round(($totalFailed / $totalContacts) * 100, 2) . '%' : '0%';
$totalAcceptedInPercent = $totalContacts > 0 ? round(($totalAccepted / $totalContacts) * 100, 2) . '%' : '0%';
$totalProcessingInPercent = $totalContacts > 0 ? round(($totalProcessing / $totalContacts) * 100, 2) . '%' : '0%';
$totalWaitingInPercent = $totalContacts > 0 ? round(($totalWaiting / $totalContacts) * 100, 2) . '%' : '0%';
$totalSentInPercent = $totalContacts > 0 ? round(($totalSent / $totalContacts) * 100, 2) . '%' : '0%';

// Calculate the total percentage of processed messages
$totalProcessedInPercent = $totalContacts > 0 ? round(($totalProcessed / $totalContacts) * 100, 2) . '%' : '0%';

// Ensure we don't count delivered messages twice
$totalDeliveredCount = $totalDelivered + $totalRead;
@endphp

<div class="container-fluid mt-lg--6 lw-campaign-window-{{ $campaign->_uid }}" x-cloak x-data="initialRequiredData">
    <div class="row">
        <!-- Header Section -->
        <div class="col-12 mb-3">
            <div class="mt-5 d-flex justify-content-between align-items-center">
                <h1 class="page-title mb-0" style="color: #28A745;">
                    <i class="fas fa-volleyball-ball me-2" style="color: #28A745; margin-left: 10px;"></i>{{ __tr(' Campaign Dashboard') }}
                </h1>
                <div class="d-flex">
                    <a class="lw-btn btn btn-modern btn-modern-secondary btn-rounded animate__animated animate__fadeIn me-2"
                       href="{{ route('vendor.campaign.read.list_view') }}">
                        <i class="fa fa-arrow-left me-2"></i>{{ __tr(' Back to Campaigns') }}
                    </a>
                    <a class="lw-btn btn btn-modern btn-modern-primary btn-rounded animate__animated animate__fadeIn"
                       href="{{ route('vendor.campaign.new.view') }}">
                        <i class="fas fa-plus-circle me-2"></i>{{ __tr(' Create New Campaign') }}
                    </a>
                </div>
            </div>
        </div>
        <!-- Campaign Information Table -->
        <div class="col-12 mb-4">
            <div class="modern-table-container">
                <!-- Table Header with Controls -->
                <div class="table-header-controls">
                    <div class="table-title-section">
                        <h2 class="table-title">{{ __tr('Campaign Information') }}</h2>
                        @if($campaign->status == 5)
                            <div class="status-badge status-archived ms-3">
                                <i class="fa fa-archive status-icon"></i>
                                <span class="status-text">{{ __tr('Archived') }}</span>
                            </div>
                        @endif
                    </div>
                    <div class="table-controls">
                        <div class="campaign-status-display">
                            <div class="status-badge status-warning">
                                <i class="fa fa-info-circle status-icon"></i>
                                <span class="status-text" x-text="statusText"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modern Campaign Info Table - Horizontal Layout -->
                <div class="table-responsive">
                    <table class="table modern-datatable campaign-info-table-horizontal">
                        <thead>
                            <tr>
                                <th class="info-header">
                                    <i class="fas fa-bullhorn me-2 text-primary"></i>
                                    {{ __tr('Campaign Name') }}
                                </th>
                                <th class="info-header">
                                    <i class="fas fa-clock me-2 text-success"></i>
                                    {{ __tr('Execution Scheduled at') }}
                                </th>
                                <th class="info-header">
                                    <i class="fas fa-file-alt me-2 text-info"></i>
                                    {{ __tr('Template Name') }}
                                </th>
                                <th class="info-header">
                                    <i class="fas fa-language me-2 text-warning"></i>
                                    {{ __tr('Template Language') }}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="info-value-horizontal">
                                    <span class="campaign-title">{{ $campaign->title }}</span>
                                </td>
                                <td class="info-value-horizontal">
                                    @if ($campaign->scheduled_at > now())
                                        <div class="scheduled-time">
                                            <span class="time-badge time-waiting">
                                                <i class="fa fa-calendar-alt me-2"></i>
                                                {{ formatDiffForHumans($campaign->scheduled_at, 3) }}
                                            </span>
                                        </div>
                                    @else
                                        <template x-if="(executedCount == 0) && inQueuedCount">
                                            <div class="scheduled-time">
                                                <span class="time-badge time-waiting">
                                                    <i class="fa fa-spin fa-spinner me-2"></i>
                                                    {{ __tr('Awaiting execution') }}
                                                </span>
                                            </div>
                                        </template>
                                    @endif

                                    @if ($campaign->timezone and getVendorSettings('timezone') != $campaign->timezone)
                                        <div class="timezone-info-horizontal mt-2">
                                            <small class="text-muted">
                                                {!! __tr('__scheduledAt__ as per your account timezone which is __selectedTimezone__', [
                                                    '__scheduledAt__' => formatDateTime($campaign->scheduled_at),
                                                    '__selectedTimezone__' => '<strong>'. getVendorSettings('timezone') .'</strong>'
                                                ]) !!}
                                            </small>
                                            <br>
                                            <small class="text-muted">
                                                {!! __tr('Campaign scheduled on __scheduledAt__ as per the __selectedTimezone__ timezone', [
                                                    '__scheduledAt__' => formatDateTime($campaign->scheduled_at_by_timezone, null, null, $campaign->timezone),
                                                    '__selectedTimezone__' => '<strong>'. $campaign->timezone .'</strong>'
                                                ]) !!}
                                            </small>
                                        </div>
                                    @else
                                        <div class="scheduled-time-display mt-1">
                                            <small class="template-name">{{ formatDateTime($campaign->scheduled_at) }}</small>
                                        </div>
                                    @endif
                                </td>
                                <td class="info-value-horizontal">
                                    <span class="template-name">{{ $campaign->template_name }}</span>
                                </td>
                                <td class="info-value-horizontal">
                                    <span class="template-language">{{ $campaign->template_language }}</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Table Footer -->
                <div class="table-footer">
                    <div class="table-info">
                        <span class="text-muted">{{ __tr('Campaign Details') }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12">
            <!-- Modern Statistics Cards Section -->
            <div class="row mb-4">
                {{-- total contacts --}}
                <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                    <div class="modern-stats-card animate__animated animate__fadeInUp">
                        <div class="stats-card-header">
                            <div class="stats-icon-container bg-gradient-info">
                                <i class="fas fa-users stats-icon"></i>
                            </div>
                            <div class="stats-trend">
                                <i class="fas fa-arrow-up text-success"></i>
                            </div>
                        </div>
                        <div class="stats-card-body">
                            <h3 class="stats-title">{{ __tr('Total Contacts') }}</h3>
                            <div class="stats-value" x-text="totalContacts"></div>
                            <div class="stats-description">
                                @if ($isAllContacts)
                                    <span class="stats-badge stats-badge-primary">{{ __tr('All contacts') }}</span>
                                @else
                                    <span class="stats-badge stats-badge-secondary">{{ __tr('Selected groups:') }}</span>
                                    @foreach ($selectedGroups as $selectedGroup)
                                        <span class="stats-badge stats-badge-warning">{{ $selectedGroup['title'] }}</span>
                                    @endforeach
                                @endif
                                @if ($isRestrictByTemplateContactLanguage)
                                    <div class="mt-2">
                                        <span class="stats-badge stats-badge-info">
                                            {!! __tr('Language: __languageCode__', [
                                                '__languageCode__' => e($campaign->template_language)
                                            ]) !!}
                                        </span>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="stats-card-footer">
                            <div class="stats-progress">
                                <div class="progress-bar bg-gradient-info" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- /total contacts --}}
                {{-- sent messages --}}
                <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                    <div class="modern-stats-card animate__animated animate__fadeInUp" style="animation-delay: 1.6s;">
                        <div class="stats-card-header">
                            <div class="stats-icon-container bg-gradient-primary">
                                <i class="fas fa-check stats-icon"></i>
                            </div>
                            <div class="stats-trend">
                                <i class="fas fa-arrow-up text-success"></i>
                            </div>
                        </div>
                        <div class="stats-card-body">
                            <h3 class="stats-title">{{ __tr('Single Tick Delivered') }}</h3>
                            <div class="stats-value" x-text="totalSentInPercent"></div>
                            <div class="stats-description">
                                <span class="stats-count" x-text="__Utils.formatAsLocaleNumber(totalSent)"></span>
                                <span class="stats-label">{{ __tr('Messages') }}</span>
                            </div>
                        </div>
                        <div class="stats-card-footer">
                            <div class="stats-progress">
                                <div class="progress-bar bg-gradient-primary" x-bind:style="'width: ' + (totalSent / totalContacts * 100) + '%'"></div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- /sent messages --}}
                {{-- delivered to --}}
                <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                    <div class="modern-stats-card animate__animated animate__fadeInUp" style="animation-delay: 1.6s;">
                        <div class="stats-card-header">
                            <div class="stats-icon-container bg-gradient-success">
                                <i class="fas fa-check-double stats-icon"></i>
                            </div>
                            <div class="stats-trend">
                                <i class="fas fa-arrow-up text-success"></i>
                            </div>
                        </div>
                        <div class="stats-card-body">
                            <h3 class="stats-title">{{ __tr('Double Tick Delivered') }}</h3>
                            <div class="stats-value" x-text="totalDeliveredInPercent"></div>
                            <div class="stats-description">
                                <span class="stats-count" x-text="__Utils.formatAsLocaleNumber(totalDelivered)"></span>
                                <span class="stats-label">{{ __tr('Contacts') }}</span>
                            </div>
                        </div>
                        <div class="stats-card-footer">
                            <div class="stats-progress">
                                <div class="progress-bar bg-gradient-success" x-bind:style="'width: ' + (totalDelivered / totalContacts * 100) + '%'"></div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- /delivered to --}}
                {{-- read by --}}
                <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                    <div class="modern-stats-card animate__animated animate__fadeInUp" style="animation-delay: 1.6s;">
                        <div class="stats-card-header">
                            <div class="stats-icon-container bg-gradient-info">
                                <i class="fas fa-eye stats-icon"></i>
                            </div>
                            <div class="stats-trend">
                                <i class="fas fa-arrow-up text-success"></i>
                            </div>
                        </div>
                        <div class="stats-card-body">
                            <h3 class="stats-title">{{ __tr('Total Read') }}</h3>
                            <div class="stats-value" x-text="totalReadInPercent"></div>
                            <div class="stats-description">
                                <span class="stats-count" x-text="__Utils.formatAsLocaleNumber(totalRead)"></span>
                                <span class="stats-label">{{ __tr('Contacts') }}</span>
                            </div>
                        </div>
                        <div class="stats-card-footer">
                            <div class="stats-progress">
                                <div class="progress-bar bg-gradient-info" x-bind:style="'width: ' + (totalRead / totalContacts * 100) + '%'"></div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- /read by --}}
                {{-- processing messages --}}
                <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                    <div class="modern-stats-card animate__animated animate__fadeInUp" style="animation-delay: 1.6s;">
                        <div class="stats-card-header">
                            <div class="stats-icon-container bg-gradient-warning">
                                <i class="fas fa-spinner fa-spin stats-icon"></i>
                            </div>
                            <div class="stats-trend">
                                <i class="fas fa-clock text-warning"></i>
                            </div>
                        </div>
                        <div class="stats-card-body">
                            <h3 class="stats-title">{{ __tr('Processing') }}</h3>
                            <div class="stats-value" x-text="totalProcessingInPercent"></div>
                            <div class="stats-description">
                                <span class="stats-count" x-text="__Utils.formatAsLocaleNumber(totalProcessing)"></span>
                                <span class="stats-label">{{ __tr('Messages') }}</span>
                            </div>
                        </div>
                        <div class="stats-card-footer">
                            <div class="stats-progress">
                                <div class="progress-bar bg-gradient-warning" x-bind:style="'width: ' + (totalProcessing / totalContacts * 100) + '%'"></div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- /processing messages --}}

                {{-- accepted messages --}}
                <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                    <div class="modern-stats-card animate__animated animate__fadeInUp" style="animation-delay: 1.6s;">
                        <div class="stats-card-header">
                            <div class="stats-icon-container bg-gradient-secondary">
                                <i class="fas fa-check-circle stats-icon"></i>
                            </div>
                            <div class="stats-trend">
                                <i class="fas fa-arrow-up text-success"></i>
                            </div>
                        </div>
                        <div class="stats-card-body">
                            <h3 class="stats-title">{{ __tr('Meta Accepted') }}</h3>
                            <div class="stats-value" x-text="totalAcceptedInPercent"></div>
                            <div class="stats-description">
                                <span class="stats-count" x-text="__Utils.formatAsLocaleNumber(totalAccepted)"></span>
                                <span class="stats-label">{{ __tr('Messages') }}</span>
                            </div>
                        </div>
                        <div class="stats-card-footer">
                            <div class="stats-progress">
                                <div class="progress-bar bg-gradient-secondary" x-bind:style="'width: ' + (totalAccepted / totalContacts * 100) + '%'"></div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- /accepted messages --}}
                {{-- failed --}}
                <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                    <div class="modern-stats-card animate__animated animate__fadeInUp" style="animation-delay: 1.6s;">
                        <div class="stats-card-header">
                            <div class="stats-icon-container bg-gradient-danger">
                                <i class="fas fa-exclamation-triangle stats-icon"></i>
                            </div>
                            <div class="stats-trend">
                                <i class="fas fa-arrow-down text-danger"></i>
                            </div>
                        </div>
                        <div class="stats-card-body">
                            <h3 class="stats-title">{{ __tr('Total Failed') }}</h3>
                            <div class="stats-value" x-text="__Utils.formatAsLocaleNumber(totalFailedInPercent)"></div>
                            <div class="stats-description">
                                <span class="stats-count" x-text="__Utils.formatAsLocaleNumber(totalFailed)"></span>
                                <span class="stats-label">{{ __tr('Contacts') }}</span>
                            </div>
                        </div>
                        <div class="stats-card-footer">
                            <div class="stats-progress">
                                <div class="progress-bar bg-gradient-danger" x-bind:style="'width: ' + (totalFailed / totalContacts * 100) + '%'"></div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- /failed --}}

                {{-- total processed messages --}}
                <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                    <div class="modern-stats-card animate__animated animate__fadeInUp" style="animation-delay: 1.7s;">
                        <div class="stats-card-header">
                            <div class="stats-icon-container bg-gradient-dark">
                                <i class="fas fa-chart-line stats-icon"></i>
                            </div>
                            <div class="stats-trend">
                                <i class="fas fa-arrow-up text-success"></i>
                            </div>
                        </div>
                        <div class="stats-card-body">
                            <h3 class="stats-title">{{ __tr('Overall Progress') }}</h3>
                            <div class="stats-value" x-text="totalProcessedInPercent"></div>
                            <div class="stats-description">
                                <span class="stats-count" x-text="__Utils.formatAsLocaleNumber(totalProcessed)"></span>
                                <span class="stats-label">{{ __tr('Messages') }}</span>
                            </div>
                        </div>
                        <div class="stats-card-footer">
                            <div class="stats-progress">
                                <div class="progress-bar bg-gradient-dark" x-bind:style="'width: ' + (totalProcessed / totalContacts * 100) + '%'"></div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- /total processed messages --}}
            </div>
            {{-- message log --}}
              <!-- Modern Tabs Section -->
        <div class="modern-tabs-container">
            <div class="d-flex justify-content-between align-items-center">
                <ul class="nav nav-tabs modern-nav-tabs" id="myTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a class="nav-link modern-tab-link <?= $pageType == "queue" ? 'active' : '' ?>"
                           href="<?= route('vendor.campaign.status.view', ['campaignUid' => $campaignUid, 'pageType' => 'queue']) ?>#logData">
                            <i class="fas fa-clock"></i><?= __tr(' Queue') ?>
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link modern-tab-link <?= $pageType == "executed" ? 'active' : '' ?>"
                           href="<?= route('vendor.campaign.status.view', ['campaignUid' => $campaignUid, 'pageType' => 'executed']) ?>#logData">
                            <i class="fas fa-check-circle"></i><?= __tr(' Executed') ?>
                        </a>
                    </li>
                </ul>

                <!-- Modern Action Buttons -->
                <div class="d-flex align-items-center gap-2">
                    @if (($pageType == "queue") and ($campaign->status == 1))
                    <template x-if="campaignStatus == 'executed' && (queueFailedCount > 0)">
                        <a class="btn btn-modern btn-modern-warning btn-sm lw-ajax-link-action"
                           data-confirm="#requeueFailedMessageConfirm-template"
                           data-method="post"
                           href="{{ route('vendor.campaign.requeue.log.write.failed', ['campaignUid' => $campaignUid]) }}">
                            <i class="fa fa-redo-alt"></i>{{ __tr(' Requeue Failed') }}
                        </a>
                    </template>
                    @endif

                    <button @click="window.reloadDT('#lwCampaignQueueLog');"
                            class="btn btn-modern btn-modern-dark btn-sm">
                        <i class="fa fa-sync"></i>{{ __tr(' Refresh') }}
                    </button>

                    @if($campaignStatus=="executed" || $campaignStatus=="processing")
                        @if($pageType== "queue")
                            <a href="{{ route('vendor.campaign.queue.log.report.write',['campaignUid' => $campaignUid]) }}"
                               data-method="post"
                               class="btn btn-modern btn-modern-success btn-sm">
                                <i class="fa fa-download"></i>{{ __tr(' Report') }}
                            </a>
                        @elseif($pageType== "executed")
                            <a href="{{ route('vendor.campaign.executed.report.write',['campaignUid' => $campaignUid]) }}"
                               data-method="post"
                               class="btn btn-modern btn-modern-success btn-sm">
                                <i class="fa fa-download me-2"></i>{{ __tr('Report') }}
                            </a>
                            <a class="btn btn-modern btn-modern-warning btn-sm lw-ajax-link-action"
                               data-confirm="#requeueFailedMessageConfirm-template"
                               data-method="post"
                               href="{{ route('vendor.campaign.requeue.log.write.failed', ['campaignUid' => $campaignUid]) }}">
                                <i class="fa fa-redo-alt me-2"></i>{{ __tr('Requeue Failed') }}
                            </a>
                        @endif
                    @endif
                </div>
            </div>
        </div>
        <!-- Modern Table Container -->
        <div class="row">
            <div class="col-12 mb-4" id="logData">
                <div class="modern-table-container">
                    <!-- Table Header with Controls -->
                    <div class="table-header-controls">
                        <div class="table-title-section">
                            <h2 class="table-title">
                                @if($pageType== "queue")
                                    {{ __tr('Campaign Queue Log') }}
                                @elseif($pageType== "executed")
                                    {{ __tr('Campaign Execution Log') }}
                                @endif
                            </h2>
                        </div>
                        <div class="table-controls">
                            <div class="entries-control">
                                <label for="entries-per-page">{{ __tr('Show') }}</label>
                                <select id="entries-per-page" class="entries-select">
                                    <option value="10">10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100" selected>100</option>
                                </select>
                                <span class="entries-text">{{ __tr('entries per page') }}</span>
                            </div>
                            <div class="search-control">
                                <input type="text" id="table-search" class="search-input" placeholder="{{ __tr('Search...') }}">
                                <i class="fas fa-search search-icon"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Modern DataTable -->
                    <div class="table-responsive">
                        @if($pageType== "queue")
                            <x-lw.datatable lw-card-classes="border-0" class="modern-datatable" data-page-length="100" id="lwCampaignQueueLog" :url="route('vendor.campaign.queue.log.list.view', ['campaignUid' => $campaignUid])">
                                <th data-orderable="true" data-name="full_name">{{ __tr('Name') }}</th>
                                <th data-orderable="true" data-name="phone_with_country_code">{{ __tr('Phone Number') }}</th>
                                <th data-orderable="true" data-order-by="true" data-order-type="desc" data-name="updated_at">{{ __tr('Last Status Updated at') }}</th>
                                <th data-template="#campaignActionColumnTemplate" data-name="null">{{ __tr('Messages') }}</th>
                            </x-lw.datatable>
                        @elseif($pageType== "executed")
                            <x-lw.datatable lw-card-classes="border-0" class="modern-datatable" data-page-length="100" id="lwCampaignQueueLog" :url="route('vendor.campaign.executed.log.list.view', ['campaignUid' => $campaignUid])">
                                <th data-orderable="true" data-name="full_name">{{ __tr('Name') }}</th>
                                <th data-orderable="true" data-name="contact_wa_id">{{ __tr('Phone Number') }}</th>
                                <th data-orderable="true" data-template="#campaignStatusMessage" data-name="messaged_at">{{ __tr('Message Delivery Status') }}</th>
                                <th data-orderable="true" data-order-by="true" data-order-type="desc" data-name="updated_at">{{ __tr('Last Status Updated at') }}</th>
                            </x-lw.datatable>
                        @endif
                    </div>

                    <!-- Table Footer with Info -->
                    <div class="table-footer">
                        <div class="table-info">
                            <span id="table-info-text">{{ __tr('Showing 0 to 0 of 0 entries') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Modern Status Templates -->

        <!-- Queue Status Template -->
        <script type="text/template" id="campaignActionColumnTemplate">
            <% if ((__tData.status != 2) && (__tData.status != 3)) { %>
                <% if (__tData.whatsapp_message_error) { %>
                    <div class="status-badge status-pending">
                        <i class="fa fa-redo status-icon"></i>
                        <span class="status-text">{{ __tr('Requeued') }}</span>
                    </div>
                    <div class="mt-1">
                        <small class="text-danger"><%- __tData.whatsapp_message_error %></small>
                    </div>
                <% } else { %>
                    <div class="status-badge status-pending">
                        <i class="fa fa-clock status-icon pulse"></i>
                        <span class="status-text">{{ __tr('Waiting') }}</span>
                    </div>
                <% } %>
            <% } else if (__tData.status == 2) { %>
                <div class="status-badge status-rejected">
                    <i class="fa fa-exclamation-triangle status-icon"></i>
                    <span class="status-text">{{ __tr('Failed') }}</span>
                </div>
                <div class="mt-1">
                    <small class="text-danger"><%- __tData.whatsapp_message_error %></small>
                </div>
            <% } else if (__tData.status == 3) { %>
                <div class="status-badge status-processing">
                    <i class="fa fa-spinner fa-spin status-icon"></i>
                    <span class="status-text">{{ __tr('Processing') }}</span>
                </div>
            <% } %>
        </script>

        <!-- Executed Status Template -->
        <script type="text/template" id="campaignStatusMessage">
            <% if (__tData.status == 'failed') { %>
                <div class="status-badge status-rejected">
                    <i class="fas fa-exclamation-triangle status-icon"></i>
                    <span class="status-text">{{ __tr('Failed') }}</span>
                </div>
                <div class="mt-1">
                    <small class="text-danger"><%- __tData.whatsapp_message_error %></small>
                </div>
            <% } else if(__tData.status == 'sent') { %>
                <div class="status-badge status-sent">
                    <i class="fas fa-check status-icon"></i>
                    <span class="status-text">{{ __tr('Sent') }}</span>
                </div>
            <% } else if(__tData.status == 'delivered') { %>
                <div class="status-badge status-delivered">
                    <i class="fas fa-check-double status-icon"></i>
                    <span class="status-text">{{ __tr('Delivered') }}</span>
                </div>
            <% } else if(__tData.status == 'read') { %>
                <div class="status-badge status-read">
                    <i class="fas fa-check-double status-icon"></i>
                    <span class="status-text">{{ __tr('Read') }}</span>
                </div>
            <% } else { %>
                <div class="status-badge status-other">
                    <i class="fa fa-info-circle status-icon"></i>
                    <span class="status-text"><%- __tData.status %></span>
                </div>
            <% } %>
        </script>

        <script type="text/template" id="requeueFailedMessageConfirm-template">
            <h2>{{ __tr('Are You Sure!') }}</h2>
            <p>{{ __tr('You want requeue all the failed messages to process it again?') }}</p>
        </script>
        </div>
    </div>
</div>
<script>
    (function() {
        'use strict';
        document.addEventListener('alpine:init', () => {
            Alpine.data('initialRequiredData', () => ({
                totalContacts: '{{ __tr($totalContacts) }}',
                totalDeliveredInPercent: '{{ __tr($totalDeliveredInPercent) }}',
                totalDelivered: '{{ __tr($totalDeliveredCount) }}',
                totalRead: '{{ __tr($totalRead) }}',
                totalReadInPercent: '{{ __tr($totalReadInPercent) }}',
                totalFailed: '{{ __tr($totalFailed) }}',
                totalFailedInPercent: '{{ __tr($totalFailedInPercent) }}',
                totalAccepted: '{{ __tr($totalAccepted) }}',
                totalAcceptedInPercent: '{{ __tr($totalAcceptedInPercent) }}',
                totalProcessing: '{{ __tr($totalProcessing) }}',
                totalProcessingInPercent: '{{ __tr($totalProcessingInPercent) }}',
                totalWaiting: '{{ __tr($totalWaiting) }}',
                totalWaitingInPercent: '{{ __tr($totalWaitingInPercent) }}',
                executedCount: {{ $messageLog->count() ?? 0 }},
                inQueuedCount: {{ $queueMessages->where('status', 1)->count() ?? 0 }},
                statusText: '{{ $statusText }}',
                campaignStatus: '{{ $campaignStatus }}',
                queueFailedCount: {{ $queueFailedCount }},
                totalSent: '{{ __tr($totalSent) }}',
                totalSentInPercent: '{{ __tr($totalSentInPercent) }}',
                totalProcessedInPercent: '{{ __tr($totalProcessedInPercent) }}',
            }));
        });
    })();
</script>
@push('appScripts')
<script>
(function($) {
    'use strict';
    // initial request
    __DataRequest.get("{{ route('vendor.campaign.status.data', ['campaignUid' => $campaignUid ]) }}");
})();
</script>
@endpush
<style>
    /* Modern Table Container Styles */
    .modern-table-container {
        background: #ffffff !important;
        border-radius: 12px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
        overflow: hidden !important;
        margin-bottom: 2rem !important;
        border: 1px solid #e9ecef !important;
    }

    /* Table Header Controls */
    .table-header-controls {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 1.5rem 2rem !important;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        border-bottom: 1px solid #dee2e6 !important;
    }

    .table-title-section {
        display: flex !important;
        align-items: center !important;
    }

    .table-title {
        font-size: 1.25rem !important;
        font-weight: 600 !important;
        color: #495057 !important;
        margin: 0 !important;
    }

    .table-controls {
        display: flex !important;
        align-items: center !important;
        gap: 1.5rem !important;
    }

    .entries-control {
        display: flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
    }

    .entries-control label {
        font-size: 0.875rem !important;
        color: #6c757d !important;
        margin: 0 !important;
    }

    .entries-select {
        padding: 0.375rem 0.75rem !important;
        border: 1px solid #ced4da !important;
        border-radius: 6px !important;
        background: #ffffff !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        min-width: 60px !important;
    }

    .entries-text {
        font-size: 0.875rem !important;
        color: #6c757d !important;
    }

    .search-control {
        position: relative !important;
    }

    .search-input {
        padding: 0.5rem 1rem 0.5rem 2.5rem !important;
        border: 1px solid #ced4da !important;
        border-radius: 8px !important;
        background: #ffffff !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        min-width: 200px !important;
        transition: all 0.3s ease !important;
    }

    .search-input:focus {
        outline: none !important;
        border-color: #007bff !important;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1) !important;
    }

    .search-icon {
        position: absolute !important;
        left: 0.75rem !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        color: #6c757d !important;
        font-size: 0.875rem !important;
    }

    /* Modern DataTable Styles */
    .modern-datatable {
        width: 100% !important;
        border-collapse: collapse !important;
    }

    .modern-datatable thead th {
        background: #f8f9fa !important;
        border: none !important;
        padding: 1rem 1.5rem !important;
        font-weight: 600 !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        position: relative !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }

    .modern-datatable thead th:hover {
        background: #e9ecef !important;
    }

    .modern-datatable tbody tr {
        border-bottom: 1px solid #f1f3f4 !important;
        transition: all 0.3s ease !important;
    }

    .modern-datatable tbody tr:hover {
        background: #f8f9fa !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    .modern-datatable tbody td {
        padding: 1rem 1.5rem !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        vertical-align: middle !important;
    }

    /* Status Badge Styles */
    .status-badge {
        display: inline-flex !important;
        align-items: center !important;
        padding: 0.5rem 1rem !important;
        border-radius: 50px !important;
        font-weight: 600 !important;
        font-size: 0.75rem !important;
        letter-spacing: 0.5px !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
        transition: all 0.3s ease !important;
        text-transform: uppercase !important;
    }

    .status-badge:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12) !important;
    }

    .status-icon {
        margin-right: 0.5rem !important;
        font-size: 0.875rem !important;
    }

    .status-approved {
        background: linear-gradient(135deg, #d4edda, #c3e6cb) !important;
        color: #155724 !important;
        border-left: 3px solid #28a745 !important;
    }

    .status-rejected {
        background: linear-gradient(135deg, #f8d7da, #f5c6cb) !important;
        color: #721c24 !important;
        border-left: 3px solid #dc3545 !important;
    }

    .status-pending {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
        color: #856404 !important;
        border-left: 3px solid #ffc107 !important;
    }

    .status-processing {
        background: linear-gradient(135deg, #cce5ff, #b3d9ff) !important;
        color: #004085 !important;
        border-left: 3px solid #007bff !important;
    }

    .status-sent {
        background: linear-gradient(135deg, #e2f0d9, #d4edda) !important;
        color: #155724 !important;
        border-left: 3px solid #28a745 !important;
    }

    .status-delivered {
        background: linear-gradient(135deg, #d4edda, #c3e6cb) !important;
        color: #155724 !important;
        border-left: 3px solid #28a745 !important;
    }

    .status-read {
        background: linear-gradient(135deg, #cce5ff, #b3d9ff) !important;
        color: #004085 !important;
        border-left: 3px solid #007bff !important;
    }

    .status-other {
        background: linear-gradient(135deg, #e9ecef, #dee2e6) !important;
        color: #495057 !important;
        border-left: 3px solid #adb5bd !important;
    }

    .status-archived {
        background: linear-gradient(135deg, #6c757d, #5a6268) !important;
        color: #ffffff !important;
        border-left: 3px solid #495057 !important;
    }

    .status-warning {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
        color: #856404 !important;
        border-left: 3px solid #ffc107 !important;
    }

    /* Campaign Information Table Styles - Horizontal Layout */
    .campaign-info-table-horizontal {
        border: none !important;
    }

    .campaign-info-table-horizontal thead th {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
        border: none !important;
        padding: 1.25rem 1rem !important;
        font-weight: 600 !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        text-align: center !important;
        vertical-align: middle !important;
        border-bottom: 2px solid #dee2e6 !important;
        white-space: nowrap !important;
    }

    .campaign-info-table-horizontal thead th:first-child {
        border-top-left-radius: 8px !important;
    }

    .campaign-info-table-horizontal thead th:last-child {
        border-top-right-radius: 8px !important;
    }

    .campaign-info-table-horizontal tbody tr {
        border-bottom: 1px solid #f1f3f4 !important;
        transition: all 0.3s ease !important;
    }

    .campaign-info-table-horizontal tbody tr:hover {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    .campaign-info-table-horizontal .info-header {
        min-width: 200px !important;
        max-width: 250px !important;
    }

    .campaign-info-table-horizontal .info-value-horizontal {
        padding: 1.5rem 1rem !important;
        font-size: 0.9rem !important;
        color: #495057 !important;
        vertical-align: middle !important;
        text-align: center !important;
        border-right: 1px solid #f1f3f4 !important;
    }

    .campaign-info-table-horizontal .info-value-horizontal:last-child {
        border-right: none !important;
    }

    .campaign-title {
        font-size: 1.25rem !important;
        font-weight: 600 !important;
        color: #22A755 !important;
    }

    .template-name,
    .template-language {
        font-size: 1.1rem !important;
        font-weight: 500 !important;
        color: #495057 !important;
    }

    .scheduled-time {
        display: flex !important;
        align-items: center !important;
        flex-wrap: wrap !important;
    }

    .time-badge {
        display: inline-flex !important;
        align-items: center !important;
        padding: 0.5rem 1rem !important;
        border-radius: 20px !important;
        font-weight: 600 !important;
        font-size: 0.875rem !important;
        margin-bottom: 0.5rem !important;
    }

    .time-future {
        background: linear-gradient(135deg, #d4edda, #c3e6cb) !important;
        color: #155724 !important;
        border: 1px solid #28a745 !important;
    }

    .time-waiting {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
        color: #856404 !important;
        border: 1px solid #ffc107 !important;
    }

    .timezone-info {
        padding: 0.75rem !important;
        background: #f8f9fa !important;
        border-radius: 8px !important;
        border-left: 3px solid #007bff !important;
    }

    .timezone-info-horizontal {
        padding: 0.5rem !important;
        background: #f8f9fa !important;
        border-radius: 6px !important;
        border-left: 2px solid #007bff !important;
        margin-top: 0.5rem !important;
        font-size: 0.8rem !important;
    }

    .scheduled-time-display {
        font-size: 0.9rem !important;
    }

    .campaign-status-display {
        display: flex !important;
        align-items: center !important;
    }

    /* Modern Statistics Cards Styles */
    .modern-stats-card {
        background: #ffffff !important;
        border-radius: 16px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
        border: 1px solid #e9ecef !important;
        transition: all 0.3s ease !important;
        overflow: hidden !important;
        position: relative !important;
        height: 100% !important;
    }

    .modern-stats-card:hover {
        transform: translateY(-8px) !important;
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15) !important;
    }

    .stats-card-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 1.5rem 1.5rem 0 1.5rem !important;
        position: relative !important;
    }

    .stats-icon-container {
        width: 60px !important;
        height: 60px !important;
        border-radius: 16px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .stats-icon-container::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3)) !important;
        z-index: 1 !important;
    }

    .stats-icon {
        font-size: 1.5rem !important;
        color: #ffffff !important;
        z-index: 2 !important;
        position: relative !important;
    }

    .stats-trend {
        font-size: 1.2rem !important;
        opacity: 0.8 !important;
    }

    .stats-card-body {
        padding: 1rem 1.5rem !important;
    }

    .stats-title {
        font-size: 0.875rem !important;
        font-weight: 600 !important;
        color: #6c757d !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        margin: 0 0 0.75rem 0 !important;
        line-height: 1.2 !important;
    }

    .stats-value {
        font-size: 2rem !important;
        font-weight: 700 !important;
        color: #495057 !important;
        margin: 0 0 0.75rem 0 !important;
        line-height: 1 !important;
    }

    .stats-description {
        display: flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
        flex-wrap: wrap !important;
    }

    .stats-count {
        font-size: 1rem !important;
        font-weight: 600 !important;
        color: #495057 !important;
    }

    .stats-label {
        font-size: 0.875rem !important;
        color: #6c757d !important;
        font-weight: 500 !important;
    }

    .stats-badge {
        display: inline-flex !important;
        align-items: center !important;
        padding: 0.25rem 0.75rem !important;
        border-radius: 12px !important;
        font-size: 0.75rem !important;
        font-weight: 600 !important;
        margin: 0.125rem !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }

    .stats-badge-primary {
        background: linear-gradient(135deg, #cce5ff, #b3d9ff) !important;
        color: #004085 !important;
        border: 1px solid #007bff !important;
    }

    .stats-badge-secondary {
        background: linear-gradient(135deg, #e9ecef, #dee2e6) !important;
        color: #495057 !important;
        border: 1px solid #6c757d !important;
    }

    .stats-badge-warning {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
        color: #856404 !important;
        border: 1px solid #ffc107 !important;
    }

    .stats-badge-info {
        background: linear-gradient(135deg, #d1ecf1, #bee5eb) !important;
        color: #0c5460 !important;
        border: 1px solid #17a2b8 !important;
    }

    .stats-card-footer {
        padding: 0 1.5rem 1.5rem 1.5rem !important;
    }

    .stats-progress {
        height: 6px !important;
        background: #f1f3f4 !important;
        border-radius: 3px !important;
        overflow: hidden !important;
        position: relative !important;
    }

    .progress-bar {
        height: 100% !important;
        border-radius: 3px !important;
        transition: width 0.6s ease !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .progress-bar::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2)) !important;
        animation: shimmer 2s infinite !important;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    /* Gradient Backgrounds */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #6e8efb, #4a6cf7) !important;
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, #0B7753, #22A755) !important;
    }

    .bg-gradient-info {
        background: linear-gradient(135deg, #17a2b8, #138496) !important;
    }

    .bg-gradient-warning {
        background: linear-gradient(135deg, #fd7e14, #e8590c) !important;
    }

    .bg-gradient-danger {
        background: linear-gradient(135deg, #dc3545, #c82333) !important;
    }

    .bg-gradient-secondary {
        background: linear-gradient(135deg, #6c757d, #5a6268) !important;
    }

    .bg-gradient-dark {
        background: linear-gradient(135deg, #495057, #343a40) !important;
    }

    /* Table Footer */
    .table-footer {
        padding: 1rem 2rem !important;
        background: #f8f9fa !important;
        border-top: 1px solid #dee2e6 !important;
    }

    .table-info {
        font-size: 0.875rem !important;
        color: #6c757d !important;
    }

    /* Modern Button Styles */
    .btn-modern {
        transition: all 0.3s ease !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08) !important;
        font-weight: 600 !important;
        letter-spacing: 0.025em !important;
        padding: 0.5rem 1rem !important;
        margin: 0 0.2rem !important;
    }

    .btn-modern:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08) !important;
    }

    .btn-modern-primary {
        background: linear-gradient(135deg, #6e8efb, #4a6cf7) !important;
        border: none !important;
        color: white !important;
    }

    .btn-modern-secondary {
        background: linear-gradient(135deg, #6edffb, #4aa3f7) !important;
        border: none !important;
        color: white !important;
    }

    .btn-modern-success {
        background: linear-gradient(135deg, #0B7753, #22A755) !important;
        border: none !important;
        color: white !important;
    }

    .btn-modern-warning {
        background: linear-gradient(135deg, #fd7e14, #e8590c) !important;
        border: none !important;
        color: white !important;
    }

    .btn-modern-dark {
        background: linear-gradient(135deg, #495057, #343a40) !important;
        border: none !important;
        color: white !important;
    }

    .btn-rounded {
        border-radius: 12px !important;
    }

    /* Modern Tabs Styles */
    .modern-tabs-container {
        background: #ffffff !important;
        border-radius: 12px 12px 0 0 !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
        border: 1px solid #e9ecef !important;
        border-bottom: none !important;
        padding: 1rem 2rem !important;
        margin-bottom: 0 !important;
    }

    .modern-nav-tabs {
        border-bottom: none !important;
    }

    .modern-tab-link {
        color: #6c757d !important;
        transition: all 0.3s ease !important;
        font-weight: 400 !important;
        border: none !important;
        /* border-radius: 8px 8px 0 0 !important; */
        /* padding: 0.75rem 1.5rem !important; */
        margin-right: 0.5rem !important;
        background: transparent !important;
    }

    .modern-tab-link:hover {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
        color: #495057 !important;
        border: none !important;
        transform: translateY(-2px) !important;
    }

    .modern-tab-link.active {
        background: linear-gradient(135deg, #007bff, #0056b3) !important;
        color: white !important;
        font-weight: 400 !important;
        border: none !important;
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2) !important;
    }

    /* Page Title Styling */
    .page-title {
        font-size: 1.75rem !important;
        font-weight: 400 !important;
        color: #333 !important;
        margin: 0 !important;
        padding: 0 !important;
        line-height: 1.2 !important;
        color: #28A745 !important;
        animation: fadeInLeft 0.5s ease-out !important;
    }

    @keyframes fadeInLeft {
        from {
            opacity: 0;
            transform: translateX(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Pulse Animation */
    .pulse {
        animation: pulse-animation 2s infinite !important;
    }

    @keyframes pulse-animation {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    /* DataTables Custom Styling */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        display: none !important;
    }

    /* Hide default DataTables elements since we have custom ones */
    .dataTables_wrapper .top {
        display: none !important;
    }

    .dataTables_wrapper .bottom {
        display: none !important;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .table-header-controls {
            flex-direction: column !important;
            gap: 1rem !important;
            align-items: stretch !important;
        }

        .table-controls {
            justify-content: space-between !important;
        }

        .modern-tabs-container .d-flex {
            flex-direction: column !important;
            gap: 1rem !important;
        }
    }

    @media (max-width: 768px) {
        .table-header-controls {
            padding: 1rem !important;
        }

        .table-controls {
            flex-direction: column !important;
            gap: 1rem !important;
        }

        .entries-control {
            justify-content: center !important;
        }

        .search-control {
            width: 100% !important;
        }

        .search-input {
            width: 100% !important;
            min-width: auto !important;
        }

        .modern-datatable thead th,
        .modern-datatable tbody td {
            padding: 0.75rem 1rem !important;
        }

        .page-title {
            font-size: 1.5rem !important;
        }

        .modern-tabs-container {
            padding: 0.5rem 1rem !important;
        }

        .modern-tab-link {
            padding: 0.5rem 1rem !important;
            font-size: 0.875rem !important;
        }

        .d-flex.justify-content-between {
            flex-direction: column !important;
            gap: 1rem !important;
        }
    }

    @media (max-width: 576px) {
        .table-title {
            font-size: 1.125rem !important;
        }

        .modern-datatable thead th,
        .modern-datatable tbody td {
            padding: 0.5rem 0.75rem !important;
            font-size: 0.8rem !important;
        }

        .status-badge {
            padding: 0.375rem 0.75rem !important;
            font-size: 0.7rem !important;
        }

        .btn-modern {
            padding: 0.375rem 0.75rem !important;
            font-size: 0.875rem !important;
        }

        /* Horizontal table responsive styles */
        .campaign-info-table-horizontal thead th {
            padding: 1rem 0.5rem !important;
            font-size: 0.75rem !important;
            min-width: 150px !important;
        }

        .campaign-info-table-horizontal .info-value-horizontal {
            padding: 1rem 0.5rem !important;
            font-size: 0.8rem !important;
        }

        .campaign-title {
            font-size: 1rem !important;
        }

        .template-name,
        .template-language {
            font-size: 0.9rem !important;
        }

        .time-badge {
            padding: 0.375rem 0.75rem !important;
            font-size: 0.75rem !important;
        }

        .timezone-info-horizontal {
            font-size: 0.7rem !important;
            padding: 0.375rem !important;
        }

        /* Modern stats cards responsive */
        .modern-stats-card {
            margin-bottom: 1rem !important;
        }

        .stats-card-header {
            padding: 1rem 1rem 0 1rem !important;
        }

        .stats-icon-container {
            width: 50px !important;
            height: 50px !important;
        }

        .stats-icon {
            font-size: 1.25rem !important;
        }

        .stats-title {
            font-size: 0.8rem !important;
        }

        .stats-value {
            font-size: 1.5rem !important;
        }

        .stats-card-body {
            padding: 0.75rem 1rem !important;
        }

        .stats-card-footer {
            padding: 0 1rem 1rem 1rem !important;
        }
    }

    /* Extra responsive styles for very small screens */
    @media (max-width: 480px) {
        .campaign-info-table-horizontal {
            font-size: 0.75rem !important;
        }

        .campaign-info-table-horizontal thead th {
            padding: 0.75rem 0.25rem !important;
            font-size: 0.7rem !important;
            min-width: 120px !important;
        }

        .campaign-info-table-horizontal .info-value-horizontal {
            padding: 0.75rem 0.25rem !important;
            font-size: 0.75rem !important;
        }

        .campaign-title {
            font-size: 0.9rem !important;
        }

        .time-badge {
            padding: 0.25rem 0.5rem !important;
            font-size: 0.7rem !important;
        }

        /* Extra small screens - stats cards */
        .modern-stats-card {
            border-radius: 12px !important;
        }

        .stats-card-header {
            padding: 0.75rem 0.75rem 0 0.75rem !important;
        }

        .stats-icon-container {
            width: 40px !important;
            height: 40px !important;
            border-radius: 12px !important;
        }

        .stats-icon {
            font-size: 1rem !important;
        }

        .stats-title {
            font-size: 0.75rem !important;
            margin-bottom: 0.5rem !important;
        }

        .stats-value {
            font-size: 1.25rem !important;
        }

        .stats-card-body {
            padding: 0.5rem 0.75rem !important;
        }

        .stats-card-footer {
            padding: 0 0.75rem 0.75rem 0.75rem !important;
        }

        .stats-badge {
            padding: 0.125rem 0.5rem !important;
            font-size: 0.7rem !important;
        }

        .stats-count {
            font-size: 0.875rem !important;
        }

        .stats-label {
            font-size: 0.75rem !important;
        }
    }
</style>

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let table;

        // Function to initialize custom functionality after DataTable is ready
        function initializeCustomFeatures() {
            table = $('#lwCampaignQueueLog').DataTable();

            if (!table) {
                // If DataTable is not ready yet, wait a bit and try again
                setTimeout(initializeCustomFeatures, 100);
                return;
            }

            // Update entries per page dropdown
            $('#entries-per-page').val(table.page.len());

            // Update table info
            updateTableInfo();

            // Add modern styling to table
            addModernTableStyling();

            // Bind custom event handlers
            bindCustomEventHandlers();
        }

        // Entries per page change handler
        $('#entries-per-page').on('change', function() {
            if (table) {
                table.page.len($(this).val()).draw();
            }
        });

        // Search functionality
        $('#table-search').on('keyup', function() {
            if (table) {
                table.search(this.value).draw();
            }
        });

        // Bind custom event handlers
        function bindCustomEventHandlers() {
            // Listen for DataTable draw events
            $('#lwCampaignQueueLog').on('draw.dt', function() {
                updateTableInfo();
                animateTableRows();
                addModernTableStyling();
                animateStatusBadges();
            });
        }

        // Update table info
        function updateTableInfo() {
            if (!table) return;

            const info = table.page.info();
            const start = info.start + 1;
            const end = info.end;
            const total = info.recordsTotal;
            const filtered = info.recordsDisplay;

            let infoText = `{{ __tr('Showing') }} ${start} {{ __tr('to') }} ${end} {{ __tr('of') }} ${filtered} {{ __tr('entries') }}`;
            if (filtered !== total) {
                infoText += ` ({{ __tr('filtered from') }} ${total} {{ __tr('total entries') }})`;
            }

            $('#table-info-text').text(infoText);
        }

        // Animate table rows
        function animateTableRows() {
            const rows = document.querySelectorAll('#lwCampaignQueueLog tbody tr');
            rows.forEach((row, index) => {
                setTimeout(() => {
                    row.classList.add('animate__animated', 'animate__fadeInUp');
                    row.style.opacity = '1';
                }, index * 50);
            });
        }

        // Animate status badges
        function animateStatusBadges() {
            const statusBadges = document.querySelectorAll('.status-badge');
            statusBadges.forEach((badge, index) => {
                badge.classList.remove('animate__fadeIn');
                void badge.offsetWidth;

                setTimeout(() => {
                    badge.classList.add('animate__fadeIn');

                    badge.addEventListener('mouseenter', function() {
                        this.classList.add('animate__pulse');
                    });

                    badge.addEventListener('mouseleave', function() {
                        this.classList.remove('animate__pulse');
                    });

                    if (badge.classList.contains('status-approved') || badge.classList.contains('status-delivered') || badge.classList.contains('status-read')) {
                        const icon = badge.querySelector('.status-icon');
                        if (icon) {
                            icon.classList.add('animate__animated', 'animate__bounceIn');
                            setTimeout(() => {
                                icon.classList.remove('animate__bounceIn');
                            }, 1000);
                        }
                    }
                }, index * 150);
            });
        }

        // Initialize animations for main buttons
        const buttons = document.querySelectorAll('.btn-modern');
        buttons.forEach((button, index) => {
            setTimeout(() => {
                button.classList.add('animate__animated', 'animate__fadeIn');
                button.style.opacity = '1';
            }, index * 150);
        });

        // Add hover effects to modern buttons
        $(document).on('mouseenter', '.btn-modern', function() {
            $(this).addClass('animate__animated animate__pulse');
        }).on('mouseleave', '.btn-modern', function() {
            $(this).removeClass('animate__animated animate__pulse');
        });

        // Add modern table styling function
        function addModernTableStyling() {
            // Add hover effects to table rows
            $('#lwCampaignQueueLog tbody tr').hover(
                function() {
                    $(this).addClass('table-row-hover');
                },
                function() {
                    $(this).removeClass('table-row-hover');
                }
            );

            // Add click effects to table headers
            $('#lwCampaignQueueLog thead th').click(function() {
                $('#lwCampaignQueueLog thead th').removeClass('sort-active');
                $(this).addClass('sort-active');
            });
        }

        // Add modern table row hover class
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .table-row-hover {
                    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
                    transform: translateY(-1px) !important;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                }
                .sort-active {
                    background: linear-gradient(135deg, #007bff, #0056b3) !important;
                    color: white !important;
                }
                .sort-active .sort-icon {
                    color: white !important;
                }
            `)
            .appendTo('head');

        // Start initialization after a short delay to ensure DataTable is ready
        setTimeout(initializeCustomFeatures, 500);
    });
</script>
@endsection

@endsection