
<?php if (isset($component)) { $__componentOriginal49c2f9c26fb91807a4f87ab8f845e982 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal49c2f9c26fb91807a4f87ab8f845e982 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.datatable','data' => ['lwCardClasses' => 'border-0','dataPageLength' => '100','id' => 'lwCampaignQueueLog','url' => route('vendor.campaign.queue.log.list.view', ['campaignUid' => $campaignUid])]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.datatable'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['lw-card-classes' => 'border-0','data-page-length' => '100','id' => 'lwCampaignQueueLog','url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('vendor.campaign.queue.log.list.view', ['campaignUid' => $campaignUid]))]); ?>
    <th data-orderable="true" data-name="full_name"><?php echo e(__tr('Name')); ?></th>
    
    <th data-orderable="true" data-name="phone_with_country_code"><?php echo e(__tr('Phone Number')); ?></th>
    <th data-orderable="true" data-order-by="true" data-order-type="desc" data-name="updated_at"><?php echo e(__tr('Last Status Updated at')); ?></th>
    <th data-template="#campaignActionColumnTemplate" data-name="null"><?php echo e(__tr('Messages')); ?></th>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal49c2f9c26fb91807a4f87ab8f845e982)): ?>
<?php $attributes = $__attributesOriginal49c2f9c26fb91807a4f87ab8f845e982; ?>
<?php unset($__attributesOriginal49c2f9c26fb91807a4f87ab8f845e982); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal49c2f9c26fb91807a4f87ab8f845e982)): ?>
<?php $component = $__componentOriginal49c2f9c26fb91807a4f87ab8f845e982; ?>
<?php unset($__componentOriginal49c2f9c26fb91807a4f87ab8f845e982); ?>
<?php endif; ?>
 <!-- action template -->
 <script type="text/template" id="campaignActionColumnTemplate">
    <!--  status -->
    <% if ((__tData.status != 2) && (__tData.status != 3)) { %>
        <% if (__tData.whatsapp_message_error) { %>
        <span class="text-muted"><?php echo e(__tr('requeued and waiting ..')); ?></span>
        <small class="text-danger"><%- __tData.whatsapp_message_error %></small>
        <% } else { %>
            <span class="text-muted"><?php echo e(__tr('waiting ..')); ?></span>
        <% } %>
     <% } else if (__tData.status == 2) { %>
        <span class="text-danger"><%- __tData.whatsapp_message_error %></span>
    <% } else if (__tData.status == 3) { %>
        <span class="text-muted"><?php echo e(__tr('processing ..')); ?></span>
    <% } %>
</script>
            <!-- / status -->
<?php /**PATH C:\xampp\htdocs\omx-flow-new\resources\views/whatsapp/campaign-queue-log-partial.blade.php ENDPATH**/ ?>