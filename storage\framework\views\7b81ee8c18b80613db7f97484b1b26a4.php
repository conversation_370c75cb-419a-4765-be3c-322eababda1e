<style>
.lw-whatsapp-carousel-container {
    margin: 10px 0;
}

.lw-whatsapp-carousel-container .carousel-card-preview {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.lw-whatsapp-carousel-container .carousel-card-preview .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 8px;
}

.lw-whatsapp-carousel-container .carousel-card-preview .card-body {
    padding: 8px;
}

.lw-whatsapp-carousel-container .carousel-card-preview .card-footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 4px;
}

.lw-whatsapp-carousel-container .carousel-card-preview .list-group-item {
    background: transparent;
    border: none;
    padding: 4px 8px;
    font-size: 12px;
    color: #007bff;
    cursor: pointer;
}

.lw-whatsapp-carousel-container .carousel-card-preview .list-group-item:hover {
    background: #e9ecef;
}

.lw-whatsapp-carousel-container .d-flex {
    overflow-x: auto;
    padding-bottom: 10px;
}

.lw-whatsapp-carousel-container .d-flex::-webkit-scrollbar {
    height: 6px;
}

.lw-whatsapp-carousel-container .d-flex::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.lw-whatsapp-carousel-container .d-flex::-webkit-scrollbar-thumb {
    background: #269C4C;
    border-radius: 3px;
}
</style>

<div class="lw-whatsapp-preview-container">
    <img class="lw-whatsapp-preview-bg" src="<?php echo e(asset('imgs/wa-message-bg.png')); ?>" alt="">
    <div class="lw-whatsapp-preview">
        <div class="card ">
            <?php $__currentLoopData = $templateComponents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $templateComponent): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($templateComponent['type'] == 'HEADER'): ?>
            <?php if($templateComponent['format'] != 'TEXT'): ?>
            <div class="lw-whatsapp-header-placeholder">
                <?php if($templateComponent['format'] == 'LOCATION'): ?>
                <i class="fa fa-5x fa-map-marker-alt text-white"></i>
                <?php elseif($templateComponent['format'] == 'VIDEO'): ?>
                <i class="fa fa-5x fa-play-circle text-white"></i>
                <?php elseif($templateComponent['format'] == 'IMAGE'): ?>
                <i class="fa fa-5x fa-image text-white"></i>
                <?php elseif($templateComponent['format'] == 'DOCUMENT'): ?>
                <i class="fa fa-5x fa-file-alt text-white"></i>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            <?php if($templateComponent['format'] == 'LOCATION'): ?>
            <div class="lw-whatsapp-location-meta bg-secondary p-2">
                <small>{{location_name}}</small><br>
                <small>{{address}}</small>
            </div>
            <?php elseif($templateComponent['format'] == 'TEXT'): ?>
            <div class="lw-whatsapp-body mb--3">
                <?php
                $exampleHeaderItems = [
                "\n" => '<br>',
                ];
                ?>
                <?php if(isset($templateComponent['example'])): ?>
                <?php
                $headerTextItems = $templateComponent['example']['header_text'];
                $exampleHeaderTextItemIndex = 1;
                foreach ($headerTextItems as $headerTextItem) {
                    $exampleHeaderItems["{{{$exampleHeaderTextItemIndex}}}"] = "{{Header $exampleHeaderTextItemIndex}}";
                    $exampleHeaderTextItemIndex++;
                }
                ?>
                <?php endif; ?>
                <strong><?= strtr($templateComponent['text'], $exampleHeaderItems) ?></strong>
            </div>
            <?php endif; ?>
            <?php endif; ?>
            <?php if($templateComponent['type'] == 'BODY'): ?>
            <div class="lw-whatsapp-body">
                <?php
                $exampleBodyItems = [
                "\n" => '<br>',
                ];
                ?>
                <?= formatWhatsAppText(strtr($templateComponent['text'], $exampleBodyItems)) ?>
            </div>
            <?php endif; ?>
            <?php if($templateComponent['type'] == 'FOOTER'): ?>
            <div class="lw-whatsapp-footer text-muted">
                <?php echo e($templateComponent['text']); ?>

            </div>
            <?php endif; ?>
            <?php if($templateComponent['type'] == 'BUTTONS'): ?>
            <div class="card-footer lw-whatsapp-buttons">
                <div class="list-group list-group-flush lw-whatsapp-buttons">
                    <?php $__currentLoopData = $templateComponent['buttons']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $templateComponentButton): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="list-group-item">
                        <?php if($templateComponentButton['type'] == 'URL'): ?>
                        <i class="fas fa-external-link-square-alt"></i>
                        <?php elseif($templateComponentButton['type'] == 'QUICK_REPLY'): ?>
                        <i class="fa fa-reply"></i>
                        <?php elseif($templateComponentButton['type'] == 'PHONE_NUMBER'): ?>
                        <i class="fa fa-phone-alt"></i>
                        <?php elseif($templateComponentButton['type'] == 'VOICE_CALL'): ?>
                        <i class="fa fa-phone-alt"></i>
                        <?php elseif($templateComponentButton['type'] == 'COPY_CODE'): ?>
                        <i class="fa fa-copy"></i>
                        <?php endif; ?>
                        <?php echo e($templateComponentButton['text']); ?>

                    </div>
                    <?php if(($loop->count > 2) and ($loop->index == 1)): ?>
                    <div class="list-group-item"><i class="fa fa-menu"></i> <?php echo e(__tr('See all options')); ?> <br><small class="text-orange"><?php echo e(__tr('More than 3 buttons will be shown in the list by clicking')); ?></small></div>
                    <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php endif; ?>
            <?php if($templateComponent['type'] == 'CAROUSEL'): ?>
            <div class="lw-whatsapp-carousel-container">
                <div class="d-flex overflow-auto pb-2" style="gap: 10px;">
                    <?php $__currentLoopData = $templateComponent['cards']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $card): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="card shadow-sm carousel-card-preview" style="min-width: 200px; max-width: 200px;">
                        <?php $__currentLoopData = $card['components']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cardComponent): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($cardComponent['type'] == 'HEADER'): ?>
                                <?php if($cardComponent['format'] == 'PRODUCT'): ?>
                                <div class="card-header bg-light text-center">
                                    <i class="fa fa-shopping-bag"></i>
                                    <small class="d-block"><?php echo e(__tr('Product from Catalog')); ?></small>
                                </div>
                                <?php elseif($cardComponent['format'] == 'IMAGE'): ?>
                                <div class="card-header p-0">
                                    <div class="lw-whatsapp-header-placeholder" style="height: 120px;">
                                        <i class="fa fa-3x fa-image text-white"></i>
                                    </div>
                                </div>
                                <?php elseif($cardComponent['format'] == 'VIDEO'): ?>
                                <div class="card-header p-0">
                                    <div class="lw-whatsapp-header-placeholder" style="height: 120px;">
                                        <i class="fa fa-3x fa-play-circle text-white"></i>
                                    </div>
                                </div>
                                <?php endif; ?>
                            <?php endif; ?>
                            <?php if($cardComponent['type'] == 'BODY'): ?>
                            <div class="card-body p-2">
                                <div class="lw-whatsapp-body" style="font-size: 13px;">
                                    <?php echo e($cardComponent['text']); ?>

                                </div>
                            </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <?php $__currentLoopData = $card['components']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cardComponent): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($cardComponent['type'] == 'BUTTONS'): ?>
                            <div class="card-footer p-1">
                                <div class="list-group list-group-flush">
                                    <?php $__currentLoopData = $cardComponent['buttons']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $button): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="list-group-item p-1 text-center" style="font-size: 12px;">
                                        <?php if($button['type'] == 'URL'): ?>
                                        <i class="fas fa-external-link-square-alt"></i>
                                        <?php elseif($button['type'] == 'QUICK_REPLY'): ?>
                                        <i class="fa fa-reply"></i>
                                        <?php elseif($button['type'] == 'SPM'): ?>
                                        <i class="fa fa-shopping-bag"></i>
                                        <?php endif; ?>
                                        <?php echo e($button['text']); ?>

                                    </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</div><?php /**PATH C:\xampp\htdocs\omx-flow-new\resources\views/whatsapp-service/template-preview-partial.blade.php ENDPATH**/ ?>