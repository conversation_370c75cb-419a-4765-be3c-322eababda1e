<?php
$onlyTemplatePreview = request()->has('only-preview');
?>
<div class="row" x-cloak>
    <?php if(!$onlyTemplatePreview): ?>
    <div class="col-sm-12 col-md-8 col-lg-6 lw-template-structure-form">
        <input type="hidden" name="template_uid" value="<?php echo e($template->_uid); ?>">
        <fieldset 
            class="p-4 rounded mb-4"
            style="border: 1px solid #22A755; background-color: #f3fff5; max-width: 600px; margin: auto;">

            <legend class="text-success fw-bold d-flex justify-content-between align-items-center" style="font-size: 1.25rem;">
                <?php echo e(__tr('Template')); ?>

                <template x-if="selectedTemplate">
                    <button class="btn btn-outline-success btn-sm ms-auto" @click.prevent="selectedTemplate = ''">
                        <?php echo e(__tr('Change')); ?>

                    </button>
                </template>
            </legend>

            <table class="table table-bordered table-sm mt-3" style="background-color: #ffffff;">
                <tbody>
                    <tr>
                        <th style="width: 40%;"><?php echo e(__tr('Template Name')); ?></th>
                        <td><strong><?php echo e($template->template_name); ?></strong></td>
                    </tr>
                    <tr>
                        <th><?php echo e(__tr('Language Code')); ?></th>
                        <td><strong><?php echo e($template->language); ?></strong></td>
                    </tr>
                    <tr>
                        <th><?php echo e(__tr('Category')); ?></th>
                        <td><strong><?php echo e($template->category); ?></strong></td>
                    </tr>
                </tbody>
            </table>
        </fieldset>

        
        <?php if($headerFormat): ?>
        <fieldset class="lw-template-header-variables-container">
            <legend><?php echo e(__tr('Header')); ?></legend>
            <?php if($headerFormat == 'LOCATION'): ?>
            <h3><?php echo e(__tr('Location Details')); ?></h3>
            <?php echo $__env->make('whatsapp-service.template-partial', [
            'parameters' => [
            'location_latitude',
            'location_longitude',
            'location_name',
            'location_address',
            ],
            'subjectType' => 'header',
            ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php elseif($headerFormat == 'TEXT' and !__isEmpty($headerParameters)): ?>
            <?php echo $__env->make('whatsapp-service.template-partial', [
            'parameters' => $headerParameters,
            'subjectType' => 'header',
            ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php elseif($headerFormat == 'TEXT' and __isEmpty($headerParameters)): ?>
            <div class="alert alert-info"><?php echo e(__tr('No variables available for header text.')); ?></div>
            <style>
                .lw-template-header-variables-container{display:none;}
            </style>
            <?php elseif($headerFormat == 'IMAGE'): ?>
            <div class="form-group col-md-4 col-sm-12">
                <label for="lwHeaderImageFilepond"><?php echo e(__tr('Select Image')); ?></label>
                <input id="lwHeaderImageFilepond" type="file" data-allow-revert="true"
                    data-label-idle="<?php echo e(__tr('Select Image')); ?>" class="lw-file-uploader" data-instant-upload="true"
                    data-action="<?= route('media.upload_temp_media', 'whatsapp_image') ?>" data-allowed-media='<?php echo e(getMediaRestriction('whatsapp_image')); ?>'
                    data-file-input-element="#lwHeaderImage">
                <input id="lwHeaderImage" type="hidden" value="" name="header_image" />
            </div>
            <?php elseif($headerFormat == 'VIDEO'): ?>
            <div class="form-group col-md-4 col-sm-12">
                <label for="lwHeaderVideoFilepond"><?php echo e(__tr('Select Video')); ?></label>
                <input id="lwHeaderVideoFilepond" type="file" data-allow-revert="true"
                    data-label-idle="<?php echo e(__tr('Select Video')); ?>" class="lw-file-uploader" data-instant-upload="true"
                    data-action="<?= route('media.upload_temp_media', 'whatsapp_video') ?>" data-allowed-media='<?php echo e(getMediaRestriction('whatsapp_video')); ?>'
                    data-file-input-element="#lwHeaderVideo">
                <input id="lwHeaderVideo" type="hidden" value="" name="header_video" />
            </div>
            <?php elseif($headerFormat == 'DOCUMENT'): ?>
            <div class="form-group col-md-4 col-sm-12">
                <label for="lwHeaderDocumentFilepond"><?php echo e(__tr('Select Document')); ?></label>
                <input id="lwHeaderDocumentFilepond" type="file" data-allow-revert="true"
                    data-label-idle="<?php echo e(__tr('Select Document')); ?>" class="lw-file-uploader" data-instant-upload="true"
                    data-action="<?= route('media.upload_temp_media', 'whatsapp_document') ?>" data-allowed-media='<?php echo e(getMediaRestriction('whatsapp_document')); ?>'
                    data-file-input-element="#lwHeaderDocument">
                <input id="lwHeaderDocument" type="hidden" value="" name="header_document" />
            </div>
            <?php echo $__env->make('whatsapp-service.template-partial', [
            'parameters' => [
            'header_document_name'
            ],
            'subjectType' => 'header',
            ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        </fieldset>
        <?php endif; ?>
        
        
        <?php if(!__isEmpty($bodyParameters)): ?>
        <fieldset>
            <legend><?php echo e(__tr('Body')); ?></legend>
            <?php echo $__env->make('whatsapp-service.template-partial', [
            'parameters' => $bodyParameters,
            'subjectType' => 'body',
            ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </fieldset>
        <?php endif; ?>
        
        
        <?php if(!__isEmpty($buttonParameters) or !__isEmpty($buttonItems)): ?>
        <fieldset>
            <legend><?php echo e(__tr('Buttons')); ?></legend>
            <?php echo $__env->make('whatsapp-service.template-partial', [
            'parameters' => $buttonParameters,
            'buttonItems' => $buttonItems,
            'subjectType' => 'button',
            ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php if(array_key_exists('COPY_CODE', $buttonItems)): ?>
            <label for=""><?php echo e(__tr('Code for Copy')); ?></label>
            <?php echo $__env->make('whatsapp-service.template-partial', [
            'parameters' => [
            'copy_code'
            ],
            'buttonItems' => [],
            'subjectType' => 'button',
            ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        </fieldset>
        <?php endif; ?>
        

        
        <?php if(!empty($isCarouselTemplate) && !empty($carouselCards)): ?>
        <fieldset>
            <legend><?php echo e(__tr('Carousel Cards Media')); ?></legend>
            <div class="alert alert-info">
                <i class="fa fa-info-circle"></i>
                <?php echo e(__tr('Upload media files for each carousel card that requires images or videos. Product cards will use items from your catalog.')); ?>

            </div>

            <?php $__currentLoopData = $carouselCards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cardIndex => $card): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fa fa-layer-group"></i>
                        <?php echo e(__tr('Card')); ?> <?php echo e($cardIndex + 1); ?>

                        <?php $__currentLoopData = $card['components']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $component): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($component['type'] == 'HEADER'): ?>
                                - <?php echo e(ucfirst(strtolower($component['format']))); ?> <?php echo e(__tr('Header')); ?>

                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php $__currentLoopData = $card['components']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $component): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($component['type'] == 'HEADER'): ?>
                            <?php if($component['format'] == 'IMAGE'): ?>
                            <div class="form-group">
                                <label for="lwCarouselCard<?php echo e($cardIndex); ?>ImageFilepond"><?php echo e(__tr('Select Image for Card')); ?> <?php echo e($cardIndex + 1); ?></label>
                                <input id="lwCarouselCard<?php echo e($cardIndex); ?>ImageFilepond" type="file" data-allow-revert="true"
                                    data-label-idle="<?php echo e(__tr('Select Image')); ?>" class="lw-file-uploader" data-instant-upload="true"
                                    data-action="<?= route('media.upload_temp_media', 'whatsapp_image') ?>" data-allowed-media='<?php echo e(getMediaRestriction('whatsapp_image')); ?>'
                                    data-file-input-element="#lwCarouselCard<?php echo e($cardIndex); ?>Image">
                                <input id="lwCarouselCard<?php echo e($cardIndex); ?>Image" type="hidden" value="" name="carousel_card_<?php echo e($cardIndex); ?>_image" />
                            </div>
                            <?php elseif($component['format'] == 'VIDEO'): ?>
                            <div class="form-group">
                                <label for="lwCarouselCard<?php echo e($cardIndex); ?>VideoFilepond"><?php echo e(__tr('Select Video for Card')); ?> <?php echo e($cardIndex + 1); ?></label>
                                <input id="lwCarouselCard<?php echo e($cardIndex); ?>VideoFilepond" type="file" data-allow-revert="true"
                                    data-label-idle="<?php echo e(__tr('Select Video')); ?>" class="lw-file-uploader" data-instant-upload="true"
                                    data-action="<?= route('media.upload_temp_media', 'whatsapp_video') ?>" data-allowed-media='<?php echo e(getMediaRestriction('whatsapp_video')); ?>'
                                    data-file-input-element="#lwCarouselCard<?php echo e($cardIndex); ?>Video">
                                <input id="lwCarouselCard<?php echo e($cardIndex); ?>Video" type="hidden" value="" name="carousel_card_<?php echo e($cardIndex); ?>_video" />
                            </div>
                            <?php elseif($component['format'] == 'PRODUCT'): ?>
                            <div class="alert alert-warning">
                                <i class="fa fa-shopping-bag"></i>
                                <?php echo e(__tr('This card will display a product from your catalog. No media upload required.')); ?>

                            </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    
                    <?php $__currentLoopData = $card['components']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $component): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($component['type'] == 'BODY'): ?>
                        <div class="alert alert-light">
                            <strong><?php echo e(__tr('Card Text:')); ?></strong> <?php echo e($component['text']); ?>

                        </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </fieldset>
        <?php endif; ?>
        
    </div>
    
    <div class="col-sm-12 col-md-8 col-lg-6">
        <fieldset class="position-absolute w-100">
            <legend><?php echo e(__tr('Message Preview')); ?></legend>
            <div class="card">
                <div class="card-body">
                    <?php else: ?>
                    <div class="col-12">
                    <?php endif; ?>
                    <?php echo $__env->make('whatsapp-service.template-preview-partial', [
            'bodyComponentText' => $bodyComponentText,
            'parameters' => $bodyParameters,
            'subjectType' => 'body',
            'templateComponents' => $templateComponents
            ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
 </div>
<?php if(!$onlyTemplatePreview): ?>
            </div>
            <div class="alert alert-light mt-5">
                <strong><?php echo e(__tr('Please note:')); ?></strong>
               <?php echo __tr('Words like {{1}}, {{abc}} etc are dynamic variables and will be replaced based on your selections.'); ?>

            </div>
            <?php endif; ?>
        </fieldset>
    </div>
</div><?php /**PATH C:\xampp\htdocs\omx-flow-new\resources\views/whatsapp-service/message-preparation.blade.php ENDPATH**/ ?>