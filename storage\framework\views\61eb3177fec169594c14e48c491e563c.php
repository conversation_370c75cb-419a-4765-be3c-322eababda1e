
<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
<div class="lw-page-content">
    <!-- Header Section -->
    <div class="lw-page-header mt-3">
        <div class="container-fluid mt-3">
            <div class="d-flex justify-content-between align-items-center border-bottom pb-3 mb-4">
                <div>
                    <h1 class="page-title mb-0" style="color: #22A755;">
                        <i class="fas fa-shopping-cart me-2"></i><?php echo e(__tr(' Order Details')); ?> - #<?php echo e($order->order_id); ?>

                    </h1>
                    <ol class="breadcrumb mb-0 mt-2">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('vendor.console')); ?>"><?php echo e(__tr('Home')); ?></a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('vendor.whatsapp.orders.list')); ?>"><?php echo e(__tr('WhatsApp Orders')); ?></a></li>
                        <li class="breadcrumb-item active"><?php echo e(__tr('Order Details')); ?></li>
                    </ol>
                </div>
                <div class="d-flex">
                    <a href="<?php echo e(route('vendor.whatsapp.orders.list')); ?>"
                       class="lw-btn btn btn-modern btn-modern-secondary btn-rounded animate__animated animate__fadeIn">
                        <i class="fa fa-arrow-left me-2"></i><?php echo e(__tr(' Back to Orders')); ?>

                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Page content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Order Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><?php echo e(__tr('Order Information')); ?></h5>
                        <span class="badge badge-<?php echo e(getStatusColor($order->status)); ?> badge-lg">
                            <?php echo e($order->status_label); ?>

                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong><?php echo e(__tr('Order ID')); ?>:</strong></td>
                                        <td><?php echo e($order->order_id); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__tr('Order Date')); ?>:</strong></td>
                                        <td><?php echo e($order->created_at->format('d M Y, h:i A')); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__tr('Payment Status')); ?>:</strong></td>
                                        <td>
                                            <?php if($order->payment_status): ?>
                                                <span class="badge badge-success"><?php echo e(ucfirst($order->payment_status)); ?></span>
                                            <?php else: ?>
                                                <span class="badge badge-warning"><?php echo e(__tr('Pending')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__tr('Currency')); ?>:</strong></td>
                                        <td><?php echo e($order->currency); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong><?php echo e(__tr('Total Amount')); ?>:</strong></td>
                                        <td><h5 class="text-success mb-0"><?php echo e($order->formatted_total_amount); ?></h5></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__tr('Payment Completed')); ?>:</strong></td>
                                        <td>
                                            <?php if($order->payment_completed_at): ?>
                                                <?php echo e($order->payment_completed_at->format('d M Y, h:i A')); ?>

                                            <?php else: ?>
                                                <?php echo e(__tr('Not completed')); ?>

                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__tr('Shipped At')); ?>:</strong></td>
                                        <td>
                                            <?php if($order->shipped_at): ?>
                                                <?php echo e($order->shipped_at->format('d M Y, h:i A')); ?>

                                            <?php else: ?>
                                                <?php echo e(__tr('Not shipped')); ?>

                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__tr('Delivered At')); ?>:</strong></td>
                                        <td>
                                            <?php if($order->delivered_at): ?>
                                                <?php echo e($order->delivered_at->format('d M Y, h:i A')); ?>

                                            <?php else: ?>
                                                <?php echo e(__tr('Not delivered')); ?>

                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Items - Modern Table Container -->
                <div class="modern-table-container mb-4">
                    <!-- Table Header with Controls -->
                    <div class="table-header-controls">
                        <div class="table-title-section">
                            <h2 class="table-title"><?php echo e(__tr('Order Items')); ?></h2>
                        </div>
                    </div>

                    <!-- Modern DataTable -->
                    <div class="table-responsive">
                        <table class="table modern-datatable" id="orderItemsTable">
                            <thead>
                                <tr>
                                    <th><?php echo e(__tr('Product')); ?></th>
                                    <th><?php echo e(__tr('Quantity')); ?></th>
                                    <th><?php echo e(__tr('Price')); ?></th>
                                    <th><?php echo e(__tr('Total')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if($order->items): ?>
                                    <?php $__currentLoopData = $order->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?php echo e($item['product_retailer_id'] ?? $item['name'] ?? __tr('Unknown Product')); ?></strong>
                                            </div>
                                            <?php if(isset($item['product_description'])): ?>
                                                <small class="text-muted"><?php echo e($item['product_description']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="quantity-badge">
                                                <span class="quantity-text"><?php echo e($item['quantity'] ?? 1); ?></span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="price-text"><?php echo e($order->currency); ?> <?php echo e(number_format($item['item_price'] ?? 0, 2)); ?></span>
                                        </td>
                                        <td>
                                            <span class="total-text"><?php echo e($order->currency); ?> <?php echo e(number_format(($item['item_price'] ?? 0) * ($item['quantity'] ?? 1), 2)); ?></span>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </tbody>
                            <tfoot class="modern-table-footer">
                                <tr>
                                    <th colspan="3"><?php echo e(__tr('Subtotal')); ?></th>
                                    <th><?php echo e($order->currency); ?> <?php echo e(number_format($order->total_amount, 2)); ?></th>
                                </tr>
                                <?php if($order->tax_amount > 0): ?>
                                <tr>
                                    <th colspan="3"><?php echo e(__tr('Tax')); ?></th>
                                    <th><?php echo e($order->currency); ?> <?php echo e(number_format($order->tax_amount, 2)); ?></th>
                                </tr>
                                <?php endif; ?>
                                <?php if($order->shipping_amount > 0): ?>
                                <tr>
                                    <th colspan="3"><?php echo e(__tr('Shipping')); ?></th>
                                    <th><?php echo e($order->currency); ?> <?php echo e(number_format($order->shipping_amount, 2)); ?></th>
                                </tr>
                                <?php endif; ?>
                                <?php if($order->discount_amount > 0): ?>
                                <tr>
                                    <th colspan="3"><?php echo e(__tr('Discount')); ?></th>
                                    <th class="discount-amount">-<?php echo e($order->currency); ?> <?php echo e(number_format($order->discount_amount, 2)); ?></th>
                                </tr>
                                <?php endif; ?>
                                <tr class="total-row">
                                    <th colspan="3"><?php echo e(__tr('Total')); ?></th>
                                    <th class="final-total"><?php echo e($order->currency); ?> <?php echo e(number_format($order->getFinalAmount(), 2)); ?></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Table Footer with Info -->
                    <div class="table-footer">
                        <div class="table-info">
                            <span id="items-info-text"><?php echo e(__tr('Showing')); ?> <?php echo e(count($order->items ?? [])); ?> <?php echo e(__tr('items')); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Payment History - Modern Table Container -->
                <?php if($payments->count() > 0): ?>
                <div class="modern-table-container mb-4">
                    <!-- Table Header with Controls -->
                    <div class="table-header-controls">
                        <div class="table-title-section">
                            <h2 class="table-title"><?php echo e(__tr('Payment History')); ?></h2>
                        </div>
                    </div>

                    <!-- Modern DataTable -->
                    <div class="table-responsive">
                        <table class="table modern-datatable" id="paymentHistoryTable">
                            <thead>
                                <tr>
                                    <th><?php echo e(__tr('Payment ID')); ?></th>
                                    <th><?php echo e(__tr('Amount')); ?></th>
                                    <th><?php echo e(__tr('Status')); ?></th>
                                    <th><?php echo e(__tr('Method')); ?></th>
                                    <th><?php echo e(__tr('Date')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <code class="payment-id"><?php echo e($payment->payment_id); ?></code>
                                    </td>
                                    <td>
                                        <span class="amount-text"><?php echo e($payment->formatted_amount); ?></span>
                                    </td>
                                    <td>
                                        <?php if($payment->is_successful): ?>
                                            <div class="status-badge status-approved">
                                                <i class="fa fa-check-circle status-icon"></i>
                                                <span class="status-text"><?php echo e($payment->status_label); ?></span>
                                            </div>
                                        <?php elseif($payment->is_failed): ?>
                                            <div class="status-badge status-rejected">
                                                <i class="fa fa-times-circle status-icon"></i>
                                                <span class="status-text"><?php echo e($payment->status_label); ?></span>
                                            </div>
                                        <?php else: ?>
                                            <div class="status-badge status-pending">
                                                <i class="fa fa-clock status-icon"></i>
                                                <span class="status-text"><?php echo e($payment->status_label); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="method-text"><?php echo e($payment->payment_method ?? __tr('N/A')); ?></span>
                                    </td>
                                    <td>
                                        <span class="date-text"><?php echo e($payment->created_at->format('d M Y, h:i A')); ?></span>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Table Footer with Info -->
                    <div class="table-footer">
                        <div class="table-info">
                            <span id="payments-info-text"><?php echo e(__tr('Showing')); ?> <?php echo e($payments->count()); ?> <?php echo e(__tr('payments')); ?></span>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Customer Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><?php echo e(__tr('Customer Information')); ?></h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong><?php echo e(__tr('Name')); ?>:</strong></td>
                                <td><?php echo e($order->customer_name ?: __tr('N/A')); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php echo e(__tr('Phone')); ?>:</strong></td>
                                <td>
                                    <a href="https://wa.me/<?php echo e($order->customer_phone); ?>" target="_blank" class="text-success">
                                        <i class="fab fa-whatsapp"></i> <?php echo e($order->customer_phone); ?>

                                    </a>
                                </td>
                            </tr>
                            <?php if($order->contact): ?>
                            <tr>
                                <td><strong><?php echo e(__tr('Contact')); ?>:</strong></td>
                                <td>
                                    <a href="<?php echo e(route('vendor.chat_message.contact.view', $order->contact->_uid)); ?>" class="btn btn-sm btn-outline-primary">
                                        <?php echo e(__tr('Chat with Customer')); ?>

                                    </a>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>

                <!-- Delivery Address -->
                <?php if($order->delivery_address): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><?php echo e(__tr('Delivery Address')); ?></h5>
                    </div>
                    <div class="card-body">
                        <address>
                            <?php echo nl2br(e($order->delivery_address)); ?>

                        </address>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Order Actions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><?php echo e(__tr('Order Actions')); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex flex-wrap gap-2 justify-content-start">
                            <button type="button"
                                class="btn text-white d-flex align-items-center gap-1 px-3 py-2 border-0"
                                onclick="showUpdateStatusModal()"
                                style="background: linear-gradient(135deg, #38AFFF, #4AD4FF);  border-radius: 8px; margin-left: 5px;">
                                <i class="fa fa-edit mr-2"></i> <?php echo e(__tr(' Update')); ?>

                            </button>

                            <?php if($order->canBeCancelled()): ?>
                            <button type="button" class="btn text-white d-flex align-items-center gap-1 px-3 py-2 border-0"
                                onclick="cancelOrder()"
                                style="background: linear-gradient(135deg, #8B0000, #b22222); border-radius: 8px; margin-left: 5px;">
                                <i class="fa fa-times mr-2"></i> <?php echo e(__tr(' Cancel')); ?>

                            </button>
                            <?php endif; ?>
                            <a href="https://wa.me/<?php echo e($order->customer_phone); ?>?text=<?php echo e(urlencode('Hi! Regarding your order #' . $order->order_id)); ?>"
                                target="_blank"
                                class="btn text-white d-flex align-items-center gap-1 px-3 py-2 border-0"
                                style="background: linear-gradient(135deg, #006400, #228B22); border-radius: 8px; margin-left: 5px;">
                                    <i class="fab fa-whatsapp mr-2"></i> <?php echo e(__tr(' Message')); ?>

                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Status Update Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo e(__tr('Update Order Status')); ?></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="updateStatusForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label><?php echo e(__tr('Current Status')); ?></label>
                        <input type="text" class="form-control" value="<?php echo e($order->status_label); ?>" readonly>
                    </div>
                    <div class="form-group">
                        <label><?php echo e(__tr('New Status')); ?></label>
                        <select name="status" class="form-control" required>
                            <option value="pending" <?php echo e($order->status == 'pending' ? 'selected' : ''); ?>><?php echo e(__tr('Pending')); ?></option>
                            <option value="awaiting_address" <?php echo e($order->status == 'awaiting_address' ? 'selected' : ''); ?>><?php echo e(__tr('Awaiting Address')); ?></option>
                            <option value="awaiting_payment" <?php echo e($order->status == 'awaiting_payment' ? 'selected' : ''); ?>><?php echo e(__tr('Awaiting Payment')); ?></option>
                            <option value="paid" <?php echo e($order->status == 'paid' ? 'selected' : ''); ?>><?php echo e(__tr('Paid')); ?></option>
                            <option value="confirmed" <?php echo e($order->status == 'confirmed' ? 'selected' : ''); ?>><?php echo e(__tr('Confirmed')); ?></option>
                            <option value="shipped" <?php echo e($order->status == 'shipped' ? 'selected' : ''); ?>><?php echo e(__tr('Shipped')); ?></option>
                            <option value="delivered" <?php echo e($order->status == 'delivered' ? 'selected' : ''); ?>><?php echo e(__tr('Delivered')); ?></option>
                            <option value="cancelled" <?php echo e($order->status == 'cancelled' ? 'selected' : ''); ?>><?php echo e(__tr('Cancelled')); ?></option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(__tr('Cancel')); ?></button>
                    <button type="submit" class="btn btn-primary"><?php echo e(__tr('Update Status')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
<style>
    /* Modern Table Container Styles */
    .modern-table-container {
        background: #ffffff !important;
        border-radius: 12px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
        overflow: hidden !important;
        margin-bottom: 2rem !important;
        border: 1px solid #e9ecef !important;
    }

    /* Table Header Controls */
    .table-header-controls {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 1.5rem 2rem !important;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        border-bottom: 1px solid #dee2e6 !important;
    }

    .table-title-section {
        display: flex !important;
        align-items: center !important;
    }

    .table-title {
        font-size: 1.25rem !important;
        font-weight: 600 !important;
        color: #495057 !important;
        margin: 0 !important;
    }

    .table-controls {
        display: flex !important;
        align-items: center !important;
        gap: 1.5rem !important;
    }

    .search-control {
        position: relative !important;
    }

    .search-input {
        padding: 0.5rem 1rem 0.5rem 2.5rem !important;
        border: 1px solid #ced4da !important;
        border-radius: 8px !important;
        background: #ffffff !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        min-width: 200px !important;
        transition: all 0.3s ease !important;
    }

    .search-input:focus {
        outline: none !important;
        border-color: #007bff !important;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1) !important;
    }

    .search-icon {
        position: absolute !important;
        left: 0.75rem !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        color: #6c757d !important;
        font-size: 0.875rem !important;
    }

    /* Modern DataTable Styles */
    .modern-datatable {
        width: 100% !important;
        border-collapse: collapse !important;
    }

    .modern-datatable thead th {
        background: #f8f9fa !important;
        border: none !important;
        padding: 1rem 1.5rem !important;
        font-weight: 600 !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        position: relative !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }

    .modern-datatable thead th:hover {
        background: #e9ecef !important;
    }

    .modern-datatable tbody tr {
        border-bottom: 1px solid #f1f3f4 !important;
        transition: all 0.3s ease !important;
    }

    .modern-datatable tbody tr:hover {
        background: #f8f9fa !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    .modern-datatable tbody td {
        padding: 1rem 1.5rem !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        vertical-align: middle !important;
    }

    /* Modern Table Footer */
    .modern-table-footer th {
        background: #f8f9fa !important;
        border-top: 2px solid #dee2e6 !important;
        padding: 1rem 1.5rem !important;
        font-weight: 600 !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
    }

    .total-row th {
        background: linear-gradient(135deg, #d4edda, #c3e6cb) !important;
        color: #155724 !important;
        font-size: 1rem !important;
        font-weight: 700 !important;
    }

    .final-total {
        color: #155724 !important;
        font-size: 1.1rem !important;
    }

    .discount-amount {
        color: #dc3545 !important;
        font-weight: 600 !important;
    }

    /* Status Badge Styles */
    .status-badge {
        display: inline-flex !important;
        align-items: center !important;
        padding: 0.5rem 1rem !important;
        border-radius: 50px !important;
        font-weight: 600 !important;
        font-size: 0.75rem !important;
        letter-spacing: 0.5px !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
        transition: all 0.3s ease !important;
        text-transform: uppercase !important;
    }

    .status-badge:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12) !important;
    }

    .status-icon {
        margin-right: 0.5rem !important;
        font-size: 0.875rem !important;
    }

    .status-approved {
        background: linear-gradient(135deg, #d4edda, #c3e6cb) !important;
        color: #155724 !important;
        border-left: 3px solid #28a745 !important;
    }

    .status-rejected {
        background: linear-gradient(135deg, #f8d7da, #f5c6cb) !important;
        color: #721c24 !important;
        border-left: 3px solid #dc3545 !important;
    }

    .status-pending {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
        color: #856404 !important;
        border-left: 3px solid #ffc107 !important;
    }

    /* Special Styling for Order Items */
    .quantity-badge {
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        background: linear-gradient(135deg, #e9ecef, #dee2e6) !important;
        color: #495057 !important;
        padding: 0.375rem 0.75rem !important;
        border-radius: 20px !important;
        font-weight: 600 !important;
        font-size: 0.875rem !important;
        min-width: 40px !important;
    }

    .price-text,
    .total-text,
    .amount-text {
        font-weight: 600 !important;
        color: #155724 !important;
        font-size: 0.9rem !important;
    }

    .payment-id {
        background: #f8f9fa !important;
        color: #495057 !important;
        padding: 0.25rem 0.5rem !important;
        border-radius: 4px !important;
        font-size: 0.8rem !important;
        border: 1px solid #dee2e6 !important;
    }

    .method-text,
    .date-text {
        color: #6c757d !important;
        font-size: 0.875rem !important;
    }

    /* Table Footer */
    .table-footer {
        padding: 1rem 2rem !important;
        background: #f8f9fa !important;
        border-top: 1px solid #dee2e6 !important;
    }

    .table-info {
        font-size: 0.875rem !important;
        color: #6c757d !important;
    }

    /* Modern Button Styles */
    .btn-modern {
        transition: all 0.3s ease !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08) !important;
        font-weight: 600 !important;
        letter-spacing: 0.025em !important;
        padding: 0.5rem 1rem !important;
        margin: 0 0.2rem !important;
    }

    .btn-modern:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08) !important;
    }

    .btn-modern-secondary {
        background: linear-gradient(135deg, #6edffb, #4aa3f7) !important;
        border: none !important;
        color: white !important;
    }

    .btn-rounded {
        border-radius: 12px !important;
    }

    /* Page Title Styling */
    .page-title {
        font-size: 1.75rem !important;
        font-weight: 600 !important;
        color: #333 !important;
        margin: 0 !important;
        padding: 0 !important;
        line-height: 1.2 !important;
        animation: fadeInLeft 0.5s ease-out !important;
    }

    @keyframes fadeInLeft {
        from {
            opacity: 0;
            transform: translateX(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .table-header-controls {
            flex-direction: column !important;
            gap: 1rem !important;
            align-items: stretch !important;
        }

        .table-controls {
            justify-content: center !important;
        }
    }

    @media (max-width: 768px) {
        .table-header-controls {
            padding: 1rem !important;
        }

        .search-control {
            width: 100% !important;
        }

        .search-input {
            width: 100% !important;
            min-width: auto !important;
        }

        .modern-datatable thead th,
        .modern-datatable tbody td,
        .modern-table-footer th {
            padding: 0.75rem 1rem !important;
        }

        .page-title {
            font-size: 1.5rem !important;
        }

        .d-flex.justify-content-between {
            flex-direction: column !important;
            gap: 1rem !important;
        }
    }

    @media (max-width: 576px) {
        .table-title {
            font-size: 1.125rem !important;
        }

        .modern-datatable thead th,
        .modern-datatable tbody td,
        .modern-table-footer th {
            padding: 0.5rem 0.75rem !important;
            font-size: 0.8rem !important;
        }

        .status-badge {
            padding: 0.375rem 0.75rem !important;
            font-size: 0.7rem !important;
        }

        .btn-modern {
            padding: 0.375rem 0.75rem !important;
            font-size: 0.875rem !important;
        }
    }
</style>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize modern table features
        initializeModernTableFeatures();

        // Initialize animations for buttons
        const buttons = document.querySelectorAll('.btn-modern');
        buttons.forEach((button, index) => {
            setTimeout(() => {
                button.classList.add('animate__animated', 'animate__fadeIn');
                button.style.opacity = '1';
            }, index * 150);
        });

        // Add hover effects to modern buttons
        $(document).on('mouseenter', '.btn-modern', function() {
            $(this).addClass('animate__animated animate__pulse');
        }).on('mouseleave', '.btn-modern', function() {
            $(this).removeClass('animate__animated animate__pulse');
        });
    });

    // Initialize modern table features
    function initializeModernTableFeatures() {
        // Search functionality for Order Items
        $('#items-search').on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase();
            filterTableRows('#orderItemsTable', searchTerm);
            updateItemsInfo();
        });

        // Search functionality for Payment History
        $('#payments-search').on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase();
            filterTableRows('#paymentHistoryTable', searchTerm);
            updatePaymentsInfo();
        });

        // Add modern styling to tables
        addModernTableStyling();

        // Animate table rows
        animateTableRows();

        // Status badge animations
        animateStatusBadges();
    }

    // Filter table rows based on search term
    function filterTableRows(tableSelector, searchTerm) {
        $(tableSelector + ' tbody tr').each(function() {
            const rowText = $(this).text().toLowerCase();
            if (rowText.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }

    // Update items info
    function updateItemsInfo() {
        const visibleRows = $('#orderItemsTable tbody tr:visible').length;
        const totalRows = $('#orderItemsTable tbody tr').length;

        let infoText = `<?php echo e(__tr('Showing')); ?> ${visibleRows} <?php echo e(__tr('of')); ?> ${totalRows} <?php echo e(__tr('items')); ?>`;
        $('#items-info-text').text(infoText);
    }

    // Update payments info
    function updatePaymentsInfo() {
        const visibleRows = $('#paymentHistoryTable tbody tr:visible').length;
        const totalRows = $('#paymentHistoryTable tbody tr').length;

        let infoText = `<?php echo e(__tr('Showing')); ?> ${visibleRows} <?php echo e(__tr('of')); ?> ${totalRows} <?php echo e(__tr('payments')); ?>`;
        $('#payments-info-text').text(infoText);
    }

    // Animate table rows
    function animateTableRows() {
        const rows = document.querySelectorAll('.modern-datatable tbody tr');
        rows.forEach((row, index) => {
            setTimeout(() => {
                row.classList.add('animate__animated', 'animate__fadeInUp');
                row.style.opacity = '1';
            }, index * 50);
        });
    }

    // Animate status badges
    function animateStatusBadges() {
        const statusBadges = document.querySelectorAll('.status-badge');
        statusBadges.forEach((badge, index) => {
            setTimeout(() => {
                badge.classList.add('animate__fadeIn');

                badge.addEventListener('mouseenter', function() {
                    this.classList.add('animate__pulse');
                });

                badge.addEventListener('mouseleave', function() {
                    this.classList.remove('animate__pulse');
                });

                if (badge.classList.contains('status-approved')) {
                    const icon = badge.querySelector('.status-icon');
                    if (icon) {
                        icon.classList.add('animate__animated', 'animate__bounceIn');
                        setTimeout(() => {
                            icon.classList.remove('animate__bounceIn');
                        }, 1000);
                    }
                }
            }, index * 150);
        });
    }

    // Add modern table styling function
    function addModernTableStyling() {
        // Add hover effects to table rows
        $('.modern-datatable tbody tr').hover(
            function() {
                $(this).addClass('table-row-hover');
            },
            function() {
                $(this).removeClass('table-row-hover');
            }
        );

        // Add click effects to table headers
        $('.modern-datatable thead th').click(function() {
            $('.modern-datatable thead th').removeClass('sort-active');
            $(this).addClass('sort-active');
        });
    }

    // Add modern table row hover class
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .table-row-hover {
                background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
                transform: translateY(-1px) !important;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            }
            .sort-active {
                background: linear-gradient(135deg, #007bff, #0056b3) !important;
                color: white !important;
            }
        `)
        .appendTo('head');
</script>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('appScripts'); ?>
<script>
$(document).ready(function() {
    // Setup CSRF token for all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Status update form submission
    $('#updateStatusForm').on('submit', function(e) {
        e.preventDefault();
        updateOrderStatus();
    });
});

function showUpdateStatusModal() {
    $('#updateStatusModal').modal('show');
}

function updateOrderStatus() {
    const formData = $('#updateStatusForm').serialize();
    
    $.ajax({
        url: "<?php echo e(route('vendor.whatsapp.orders.update_status', $order->_uid)); ?>",
        method: 'PATCH',
        data: formData,
        success: function(response) {
            if (response.reaction == 1) {
                $('#updateStatusModal').modal('hide');
                showSuccessMessage('<?php echo e(__tr("Order status updated successfully")); ?>');
                setTimeout(function() {
                    location.reload();
                }, 1500);
            } else if (response.reaction == 2 && response.data.message === 'Token Expired, Please reload and try again.') {
                showErrorMessage('<?php echo e(__tr("Session expired. The page will refresh.")); ?>');
                setTimeout(function() {
                    window.location.reload();
                }, 2000);
            } else {
                showErrorMessage(response.data.message || '<?php echo e(__tr("Failed to update order status")); ?>');
            }
        },
        error: function() {
            showErrorMessage('<?php echo e(__tr("Error updating order status")); ?>');
        }
    });
}

function cancelOrder() {
    if (confirm('<?php echo e(__tr("Are you sure you want to cancel this order?")); ?>')) {
        $.ajax({
            url: "<?php echo e(route('vendor.whatsapp.orders.update_status', $order->_uid)); ?>",
            method: 'PATCH',
            data: { status: 'cancelled' },
            success: function(response) {
                if (response.reaction == 1) {
                    showSuccessMessage('<?php echo e(__tr("Order cancelled successfully")); ?>');
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else if (response.reaction == 2 && response.data.message === 'Token Expired, Please reload and try again.') {
                    showErrorMessage('<?php echo e(__tr("Session expired. The page will refresh.")); ?>');
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                } else {
                    showErrorMessage(response.data.message || '<?php echo e(__tr("Failed to cancel order")); ?>');
                }
            },
            error: function() {
                showErrorMessage('<?php echo e(__tr("Error cancelling order")); ?>');
            }
        });
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php
function getStatusColor($status) {
    $colors = [
        'pending' => 'warning',
        'awaiting_address' => 'info',
        'awaiting_payment' => 'warning',
        'paid' => 'success',
        'confirmed' => 'success',
        'shipped' => 'primary',
        'delivered' => 'success',
        'cancelled' => 'danger',
        'refunded' => 'secondary'
    ];
    return $colors[$status] ?? 'secondary';
}
?>

<?php echo $__env->make('layouts.app', ['title' => __tr('Order Details - :orderID', ['orderID' => $order->order_id])], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-flow-new\resources\views/whatsapp-service/order-details.blade.php ENDPATH**/ ?>